import { useLayoutEffect } from 'react';

type Props = {
  className: string;
};

const BodyClassName = ({ className }: Props) => {
  useLayoutEffect(() => {
    className
      .split(' ')
      .map((classItem) => document.body.classList.add(classItem));
    return () => {
      className
        .split(' ')
        .map((classItem) => document.body.classList.remove(classItem));
    };
  }, [className]);

  return null;
};

export default BodyClassName;
