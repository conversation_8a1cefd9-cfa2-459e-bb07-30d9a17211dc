import { ComponentPropsWithoutRef, ForwardedRef, forwardRef } from 'react';
import cx from 'classnames';

import SvgIcon from 'components/SvgIcon';
import Loader from 'components/Loader/Loader';

import classNames from './Button.module.css';

export interface Props extends ComponentPropsWithoutRef<'button'> {
  theme?:
    | 'default'
    | 'primary'
    | 'danger'
    | 'dark'
    | 'toast'
    | 'white'
    | 'black'
    | 'black-green'
    | 'outline'
    | 'toolbar'
    | 'sidebar'
    | 'icon-only'
    | 'map';
  icon?: string;
  iconSize?: number;
  iconPosition?: 'left' | 'right';
  iconColor?: string;
  selected?: boolean;
  disabled?: boolean;
  fitContent?: boolean;
  loading?: boolean;
  disableWhenLoading?: boolean;
  size?: 'normal' | 'tiny' | 'small' | 'large';
  shadow?: boolean;
  className?: string;
  textClassName?: string;
  rounded?: boolean;
  counter?: number;
  counterBackground?: string;
  counterColor?: string;
}

const Button = forwardRef(
  (
    {
      children,
      theme = 'default',
      icon,
      iconPosition = 'left',
      disabled,
      fitContent,
      size = 'normal',
      loading,
      selected,
      disableWhenLoading = true,
      iconSize = 16,
      iconColor,
      shadow,
      rounded,
      counter,
      counterBackground,
      counterColor,
      className,
      textClassName,
      type = 'button',
      ...otherProps
    }: Props,
    ref: ForwardedRef<HTMLButtonElement>,
  ) => {
    const showIcon = !!icon && (disableWhenLoading || !loading);

    return (
      <button
        className={cx(
          classNames.button,
          selected && classNames.button__selected,
          loading && classNames.button__loading,
          rounded && classNames.button__rounded,
          !children && classNames.button__icon,
          fitContent && classNames.button__fit,
          classNames[`theme__${theme}`],
          classNames[`size__${size}`],
          shadow && classNames.button__shadow,
          className,
        )}
        disabled={disabled || (loading && disableWhenLoading)}
        ref={ref}
        type={type}
        {...otherProps}
      >
        {showIcon && iconPosition === 'left' && (
          <SvgIcon
            name={icon}
            color={iconColor}
            width={iconSize}
            height={iconSize}
            className={classNames.icon}
          />
        )}
        {!disableWhenLoading && loading && (
          <Loader color="inherit" loading={!!loading} inline />
        )}
        {children && (
          <div className={cx(classNames.text, textClassName)}>{children}</div>
        )}
        {!!counter && !loading && (
          <div
            className={classNames.counter}
            style={{
              ...(counterBackground && { backgroundColor: counterBackground }),
              ...(counterColor && { color: counterColor }),
            }}
          >
            {counter}
          </div>
        )}
        {showIcon && iconPosition === 'right' && (
          <SvgIcon
            name={icon}
            width={iconSize}
            height={iconSize}
            className={classNames.icon}
          />
        )}
        {disableWhenLoading && <Loader color="inherit" loading={!!loading} />}
      </button>
    );
  },
);

export default Button;
