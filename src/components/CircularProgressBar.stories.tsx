import { styled } from '@linaria/react';
import { Meta, <PERSON> } from '@storybook/react';

import CircularProgressBar, { Props } from './CircularProgressBar';

export default {
  title: 'Components/CircularProgressBar',
  component: CircularProgressBar,
  argTypes: {
    progress: {
      control: {
        type: 'range',
        min: 0,
        max: 1,
        step: 0.01,
      },
    },
  },
} as Meta;

const Wrapper = styled.div`
  margin: 64px;
`;

export const Default: Story<Props> = (props) => (
  <Wrapper>
    <CircularProgressBar {...props} />
  </Wrapper>
);
