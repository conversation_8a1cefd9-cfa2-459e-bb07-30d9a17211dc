import { animated, useSpring } from 'react-spring';

export type Props = {
  progress: number;
};

const clamp = (v: number, min: number, max: number): number => {
  if (v < min) {
    return min;
  }
  if (v > max) {
    return max;
  }
  return v;
};

function CircularProgressBar({ progress }: Props) {
  const styles = useSpring({ strokeDashoffset: clamp(1 - progress, 0, 1) });

  return (
    <svg width={20} height={20}>
      <circle
        stroke="var(--color-grey-30)"
        strokeWidth={2}
        fill="none"
        r={9}
        cx={10}
        cy={10}
      />
      <animated.circle
        stroke="var(--color-primary)"
        strokeLinecap="round"
        strokeWidth={2}
        pathLength="1"
        style={{
          strokeDasharray: 1,
          transform: 'rotate(-90deg)',
          transformOrigin: 'center',
          ...styles,
        }}
        fill="none"
        r={9}
        cx={10}
        cy={10}
      />
    </svg>
  );
}

export default CircularProgressBar;
