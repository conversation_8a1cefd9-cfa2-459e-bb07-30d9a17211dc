import { Source, Layer } from 'react-map-gl';
import { useState } from 'react';

import DocumentKeyHandler from 'components/DocumentKeyHandler/DocumentKeyHandler';

const SupportedTiles = ['20HNH', '36UVU', '36UWU', '36TVT', '36TWT'];

function TileGridLayer() {
  return (
    <Source
      id="tile-grid"
      type="vector"
      maxzoom={12}
      url="mapbox://onesoil.4wxjodbr"
    >
      <Layer
        id="tile-grid"
        source-layer="out"
        type="line"
        paint={{
          'line-color': 'white',
          'line-opacity': 0.8,
        }}
      />
      <Layer
        id="tile-grid-dev"
        source-layer="out"
        type="fill"
        filter={['in', ['get', 'Name'], ['literal', SupportedTiles]]}
        paint={{
          'fill-color': 'green',
          'fill-opacity': 0.3,
        }}
      />
      <Layer
        id="tile-grid-labels"
        source-layer="out"
        type="symbol"
        minzoom={6}
        paint={{
          'text-color': 'white',
        }}
        layout={{
          'text-field': ['get', 'Name'],
          'text-size': 32,
        }}
      />
    </Source>
  );
}

function DebugTileGridLayer() {
  const [show, setShow] = useState(false);

  return (
    <>
      <DocumentKeyHandler
        hotkey="Control+Shift+G"
        onPress={() => {
          setShow((show) => !show);
        }}
      />
      {show && <TileGridLayer />}
    </>
  );
}

export default DebugTileGridLayer;
