import { ReactN<PERSON>, useMemo, useState, useEffect } from 'react';
import { <PERSON>a, Story } from '@storybook/react';
import { Source, Layer, Marker, Popup, useMap } from 'react-map-gl';
import { useStore } from 'effector-react';
import { css } from '@linaria/core';
import intersect from '@turf/intersect';
import { geomEach } from '@turf/meta';

import { viewState$, hoveredPosition$ } from 'models/map';

import { MapView } from './MapView';

import Colors from 'styles/colors';
import polygonStripes from 'utils/polygon-stripes';
import useLayerEvents from 'utils/use-layer-events';

import DemoField from 'assets/map/demo_field.json';
import DemoYieldData from 'assets/map/f_1.json';
import DemoZones1 from 'assets/map/zones_demo_1.json';
import DemoStripe1 from 'assets/map/zones_demo_1_stripe.json';

const DemoApiToken =
  'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VyX2lkIjozLCJlbWFpbCI6ImRtaXRyeSIsImlhdCI6MTY2MDgzMjE1Mn0.FoO-yxR-wmTzxFckPQNbz26Uzw-XiqL_3p2PK_HyvTE';

css`
  :global(body.sb-show-main.sb-main-padded) {
    margin: 0;
    padding: 0;
  }
`;

export default {
  title: 'Components/2 Map/Experiments',
  component: MapView,
  argTypes: { onViewportChange: { action: 'region-change' } },
} as Meta;

export const YieldJSON: Story = () => {
  return (
    <MapView id="story">
      <Source id="data" type="geojson" data={DemoYieldData as any}>
        <Layer
          id="data"
          type="fill"
          paint={{
            'fill-color': [
              'interpolate-lab',
              ['linear'],
              ['get', 'yield_1'],
              0,
              'red',
              120,
              'limegreen',
            ],
            // 'fill-opacity': 0.6,
          }}
        />
      </Source>
    </MapView>
  );
};

YieldJSON.parameters = {
  stores: [
    {
      store: viewState$,
      value: {
        latitude: 50.655,
        longitude: 32.6326,
        zoom: 13,
      },
    },
  ],
};

export const YieldMVT: Story = () => {
  return (
    <MapView id="story">
      <Source
        id="data"
        type="vector"
        maxzoom={12}
        tiles={['http://localhost:2233/tiles/{z}/{x}/{y}.pbf']}
      >
        <Layer
          id="data"
          type="fill"
          source-layer="f_1"
          paint={{
            'fill-color': [
              'interpolate-lab',
              ['linear'],
              ['get', 'yield_1'],
              0,
              'red',
              120,
              'limegreen',
            ],
            // 'fill-opacity': 0.6,
          }}
        />
      </Source>
    </MapView>
  );
};

YieldMVT.parameters = {
  stores: [
    {
      store: viewState$,
      value: {
        latitude: 50.655,
        longitude: 32.6326,
        zoom: 13,
      },
    },
  ],
};

const UserFieldsSource = ({ children }: { children: ReactNode }) => {
  return (
    <Source
      id="own-fields"
      type="vector"
      tiles={[
        `https://platform-api.dev.onesoil.ai/en/v2/fields-users-seasons/mbtiles?tile={x},{y},{z}&token=${DemoApiToken}`,
      ]}
    >
      {children}
    </Source>
  );
};

export const UserFields: Story = () => {
  return (
    <MapView id="story">
      <UserFieldsSource>
        <Layer
          id="own-fields"
          source-layer="default"
          type="fill"
          paint={{
            'fill-color': '#9EFFB3',
            'fill-opacity': 0.6,
          }}
        />
      </UserFieldsSource>
    </MapView>
  );
};

const NativeLabelImage = () => {
  const mapRef = useMap();

  useEffect(() => {
    if (!mapRef.current) {
      return;
    }

    const map = mapRef.current.getMap();
    map.loadImage('/label-bg.png', (error?: Error, image?: any) => {
      if (error) {
        return;
      }
      if (!map.hasImage('label-bg')) {
        map.addImage('label-bg', image, {
          content: [4, 3, 12, 13],
          stretchX: [[4, 12]],
          stretchY: [[4, 12]],
        } as any);
      }
    });
  }, [mapRef]);

  return null;
};

export const FieldLabelNative: Story = ({ longLabel, formatting }) => {
  return (
    <MapView id="story">
      <NativeLabelImage />
      <UserFieldsSource>
        <Layer
          id="own-fields"
          source-layer="default"
          type="fill"
          paint={{
            'fill-color': '#9EFFB3',
            'fill-opacity': 0.6,
          }}
        />
        <Layer
          id="field-label"
          source-layer="default"
          type="symbol"
          layout={{
            'text-field': longLabel
              ? '100%\nCorn, grain\n87.7 ha\nField 87'
              : formatting
              ? [
                  'format',
                  '100%',
                  { 'text-color': 'red' },
                  '\n',
                  {},
                  'Field 1',
                  {},
                ]
              : ['get', 'title'],
            'text-justify': 'left',
            'icon-image': 'label-bg',
            'icon-text-fit': 'both',
          }}
          paint={{
            'text-color': 'white',
          }}
        />
      </UserFieldsSource>
    </MapView>
  );
};

FieldLabelNative.args = {
  longLabel: true,
  formatting: false,
};

FieldLabelNative.parameters = {
  stores: [
    {
      store: viewState$,
      value: {
        latitude: 53.818,
        longitude: 27.627,
        zoom: 13,
      },
    },
  ],
};

const FieldLabelCustom = ({ field }: { field: any }) => {
  const [size, setSize] = useState({ width: 0, height: 0 });

  return (
    <Marker latitude={field.centroid[1]} longitude={field.centroid[0]}>
      <div
        ref={(el) => {
          if (!el) {
            return;
          }
          if (size.width !== 0 || size.height !== 0) {
            return;
          }
          setSize(el.getBoundingClientRect());
        }}
        style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'flex-start',
          marginLeft: -(size.width / 2),
          marginTop: -(size.height / 2),
        }}
      >
        <div
          style={{
            borderRadius: 4,
            padding: '1px 2px',
            fontWeight: 500,
            backgroundColor:
              field.validity > 0.8 ? 'rgba(255, 255, 255, 0.8)' : Colors.yellow,
          }}
        >
          {(field.validity * 100).toFixed()}%
        </div>
        <div
          style={{
            borderRadius: 4,
            padding: '1px 2px',
            color: 'white',
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            marginTop: 3,
          }}
        >
          Maize
        </div>
        <div
          style={{
            borderRadius: 4,
            padding: '1px 2px',
            color: 'white',
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            marginTop: 3,
          }}
        >
          87 ha
        </div>
        <div
          style={{
            borderRadius: 4,
            padding: '1px 2px',
            color: 'white',
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            marginTop: 3,
          }}
        >
          {field.title}
        </div>
      </div>
    </Marker>
  );
};

export const CustomLabels: Story = () => {
  const fields = new Array(100).fill(null).map((v, index) => ({
    title: `Field ${index}`,
    validity: Math.random(),
    centroid: [Math.random() * 90, Math.random() * 90],
  }));

  return (
    <MapView id="story">
      {fields.map((field, index) => (
        <FieldLabelCustom key={index} field={field} />
      ))}
    </MapView>
  );
};

CustomLabels.parameters = {
  stores: [
    {
      store: viewState$,
      value: {
        latitude: 53.818,
        longitude: 27.627,
        zoom: 13,
      },
    },
  ],
};

const HackyLabelImage = () => {
  const mapRef = useMap();
  const canvas = useMemo(() => document.createElement('canvas'), []);
  const ctx = useMemo(() => canvas.getContext('2d'), [canvas]);
  const rendered: { [id: string]: boolean } = useMemo(() => ({}), []);

  useEffect(() => {
    if (!mapRef.current) {
      return;
    }

    const map = mapRef.current.getMap();

    const roundedRect = (
      x: number,
      y: number,
      w: number,
      h: number,
      r: number,
    ) => {
      if (!ctx) {
        return;
      }

      if (w < 2 * r) r = w / 2;
      if (h < 2 * r) r = h / 2;
      ctx.beginPath();
      ctx.moveTo(x + r, y);
      ctx.arcTo(x + w, y, x + w, y + h, r);
      ctx.arcTo(x + w, y + h, x, y + h, r);
      ctx.arcTo(x, y + h, x, y, r);
      ctx.arcTo(x, y, x + w, y, r);
      ctx.closePath();
    };

    const handleMissingImage = (event: any) => {
      if (!ctx) {
        return;
      }
      if (!event.id.startsWith('field-')) {
        return;
      }
      const fieldId = +event.id.split('-')[1];
      if (rendered[fieldId]) {
        return;
      }

      rendered[fieldId] = true;
      const validity = Math.random() + fieldId * 0;
      roundedRect(0, 0, 50, 8, 4);
      ctx.fillStyle = Colors.black;
      ctx.fill();
      roundedRect(2, 2, validity * (50 - 4), 4, 2);
      ctx.fillStyle = Colors.green;
      ctx.fill();
      if (!map.hasImage(event.id)) {
        map.addImage(event.id, ctx.getImageData(0, 0, 50, 8));
      }
    };

    map.on('styleimagemissing', handleMissingImage);
    return () => {
      map.off('styleimagemissing', handleMissingImage);
    };
  }, [mapRef, ctx, rendered]);

  return null;
};

export const HackyLabels: Story = () => {
  return (
    <MapView id="story">
      <HackyLabelImage />
      <UserFieldsSource>
        <Layer
          id="own-fields"
          source-layer="default"
          type="fill"
          paint={{
            'fill-color': '#9EFFB3',
            'fill-opacity': 0.6,
          }}
        />
        <Layer
          id="field-label"
          source-layer="default"
          type="symbol"
          layout={{
            'icon-image': ['concat', 'field-', ['get', 'id']],
          }}
          paint={{}}
        />
      </UserFieldsSource>
    </MapView>
  );
};

HackyLabels.parameters = {
  stores: [
    {
      store: viewState$,
      value: {
        latitude: 53.818,
        longitude: 27.627,
        zoom: 13,
      },
    },
  ],
};

export const SimpleZones = () => {
  return (
    <MapView id="story">
      <Source id="zones" type="geojson" data={DemoZones1 as any}>
        <Layer
          id="zones"
          type="fill"
          paint={{
            'fill-color': [
              'match',
              ['get', 'RATE'],
              30,
              '#1CC98A',
              20,
              '#FFED91',
              10,
              '#DE674D',
              'red',
            ],
          }}
        />
        <Layer
          id="labels"
          type="symbol"
          layout={{
            'text-field': [
              'match',
              ['get', 'RATE'],
              30,
              '80000',
              20,
              '74000',
              10,
              '70000',
              'none',
            ],
          }}
          paint={{}}
        />
      </Source>
    </MapView>
  );
};

SimpleZones.parameters = {
  stores: [
    {
      store: viewState$,
      value: {
        latitude: 50.882724957566744,
        longitude: 31.764177605306998,
        zoom: 13,
      },
    },
  ],
};

const makeZoneStripes = (zones: any, stripe: any) => {
  const intersectionFeatures: any[] = [];

  geomEach(zones, (geom, index, properties) => {
    if (properties?.RATE !== 30) {
      return;
    }
    intersectionFeatures.push(
      intersect(geom, stripe.features[0].geometry, {
        properties: { label: 85000 },
      }),
    );
    intersectionFeatures.push(
      intersect(geom, stripe.features[1].geometry, {
        properties: { label: 75000 },
      }),
    );
    intersectionFeatures.push(stripe.features[2]);
  });

  return {
    ...zones,
    features: intersectionFeatures.filter(Boolean),
  };
};

export const ZonesWithStripes = () => {
  const stripes = makeZoneStripes(DemoZones1, DemoStripe1);

  return (
    <MapView id="story">
      <Source id="zones" type="geojson" data={DemoZones1 as any}>
        <Layer
          id="zones"
          type="fill"
          paint={{
            'fill-color': [
              'match',
              ['get', 'RATE'],
              30,
              '#1CC98A',
              20,
              '#FFED91',
              10,
              '#DE674D',
              'red',
            ],
          }}
        />
        <Layer
          id="labels"
          type="symbol"
          layout={{
            'text-field': [
              'match',
              ['get', 'RATE'],
              30,
              '80000',
              20,
              '74000',
              10,
              '70000',
              'none',
            ],
          }}
          paint={{}}
        />
      </Source>
      <Source id="stripes" type="geojson" data={stripes}>
        <Layer
          id="stripes"
          type="fill"
          filter={['has', 'label']}
          paint={{
            'fill-color': 'rgba(0, 0, 0, 0.3)',
          }}
        />
        <Layer
          id="stripe-labels"
          type="symbol"
          filter={['has', 'label']}
          layout={{
            'text-field': ['get', 'label'],
            'text-rotate': -90,
          }}
          paint={{
            'text-color': 'white',
          }}
        />
        <Layer
          id="stripe-divider"
          type="line"
          filter={['!', ['has', 'label']]}
          paint={{
            'line-color': 'white',
          }}
        />
      </Source>
    </MapView>
  );
};

ZonesWithStripes.parameters = {
  stores: [
    {
      store: viewState$,
      value: {
        latitude: 50.882724957566744,
        longitude: 31.764177605306998,
        zoom: 13,
      },
    },
  ],
};

export const Stripes = ({ width, angle }: { width: number; angle: number }) => {
  const stripes = polygonStripes(DemoField as any, width, angle);

  return (
    <MapView id="story">
      <Source id="field" type="geojson" data={DemoField as any}>
        <Layer
          id="field"
          type="line"
          paint={{ 'line-width': 2, 'line-color': 'white' }}
        />
        <Layer
          id="field-filled"
          type="fill"
          paint={{ 'fill-color': '#9EFFB3', 'fill-opacity': 0.6 }}
        />
      </Source>
      <Source id="stripes" type="geojson" data={stripes as any}>
        <Layer id="stripes" type="line" paint={{ 'line-opacity': 0.1 }} />
      </Source>
    </MapView>
  );
};

Stripes.args = {
  width: 100,
  angle: 0,
};

Stripes.parameters = {
  stores: [
    {
      store: viewState$,
      value: {
        latitude: 50.9098,
        longitude: 33.3228,
        zoom: 13,
      },
    },
  ],
};

const FeatureTooltip = () => {
  const hoveredPosition = useStore(hoveredPosition$);
  const [hoveredFeature, setHoveredFeature] = useState<any>(null);

  useLayerEvents('zones', {
    onHover: (event, feature) => {
      setHoveredFeature(feature || null);
    },
  });

  if (!hoveredFeature) {
    return null;
  }

  return (
    <Popup
      className={css`
        :global(.mapboxgl-popup-tip) {
          display: none;
        }
      `}
      anchor="top-left"
      closeButton={false}
      offset={[10, 10]}
      {...hoveredPosition}
    >
      {hoveredFeature.properties?.RATE}
    </Popup>
  );
};

export const ZoneTooltips = () => {
  return (
    <MapView id="story">
      <Source id="zones" type="geojson" data={DemoZones1 as any}>
        <Layer
          id="zones"
          type="fill"
          paint={{
            'fill-color': [
              'match',
              ['get', 'RATE'],
              30,
              '#1CC98A',
              20,
              '#FFED91',
              10,
              '#DE674D',
              'red',
            ],
          }}
        />
        <Layer
          id="labels"
          type="symbol"
          layout={{
            'text-field': [
              'match',
              ['get', 'RATE'],
              30,
              '80000',
              20,
              '74000',
              10,
              '70000',
              'none',
            ],
          }}
          paint={{}}
        />
      </Source>
      <FeatureTooltip />
    </MapView>
  );
};

ZoneTooltips.parameters = {
  stores: [
    {
      store: viewState$,
      value: {
        latitude: 50.882724957566744,
        longitude: 31.764177605306998,
        zoom: 13,
      },
    },
  ],
};
