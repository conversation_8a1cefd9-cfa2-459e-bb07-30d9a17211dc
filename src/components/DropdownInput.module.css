.trigger {
  height: 40px;
  border-radius: 8px;
  border: 1px solid var(--color-grey-50);
  background-color: white;
  position: relative;
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
  padding: 12px 8px;
  transition: background-color 0.3s;
  outline-offset: -2px;
}

.trigger__error {
  color: var(--color-red);
  border-color: var(--color-red);
}

.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.triggerValue {
  font-size: 14px;
  line-height: 1.3;
  flex: 1 1 auto;
  padding: 2px 0;
  position: relative;
  overflow: hidden;
  text-overflow: ellipsis;
  min-width: 0;
  white-space: nowrap;
  user-select: none;
}

.placeholder {
  opacity: 0.5;
}

.label {
  display: flex;
  flex-direction: column;
}

.error {
  display: block;
  margin-top: 4px;
  margin-bottom: 4px;
  color: var(--color-red);
  font-size: 12px;
  line-height: 16px;
}
