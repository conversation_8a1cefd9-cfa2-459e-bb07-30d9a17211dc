import { Meta, Story } from '@storybook/react';
import { useState } from 'react';

import DropdownInput, { Props } from './DropdownInput';
import classNames from './DropdownInput.stories.module.css';

export default {
  title: 'Components/DropdownInput',
  component: DropdownInput,
  args: {},
  argTypes: {
    options: {
      description:
        "Array of options. Format is: `{id: 'someId', label: 'Item name'}`",
    },
    onChange: {
      description: 'Change handler',
    },
    value: {
      description: 'Initial value. Equals to `id` from option',
    },
    triggerPlaceholder: {
      description: 'Shows placeholder text on Dropdown trigger',
    },
    label: {
      description: 'Adds label before Dropdown',
    },
    error: {
      description: 'Sets validation error',
    },
  },
} as Meta;

const DemoOptions = [
  { id: '1', label: 'First value' },
  { id: '2', label: 'Second' },
  { id: '3', label: 'I have very long description' },
  { id: '4', label: 'Another item' },
];

const DemoGroups = [
  {
    id: '1',
    label: 'Some category',
    options: [
      { id: '1', label: 'First value' },
      { id: '2', label: 'Second' },
    ],
  },
  {
    id: '2',
    label: 'Some other category',
    options: [
      { id: '3', label: 'I have very long description' },
      { id: '4', label: 'Another item' },
    ],
  },
];

export const Default: Story<Props> = (args) => {
  const [val, setVal] = useState<string | null>('');
  return (
    <div className={classNames.wrapper}>
      <DropdownInput {...args} value={val} onChange={setVal} />
    </div>
  );
};

Default.args = {
  options: DemoOptions,
  icon: 'CalendarSeasons',
  triggerPlaceholder: 'Select...',
};

export const WithSearch: Story<Props> = () => {
  const [val, setVal] = useState<string | null>('');
  return (
    <div className={classNames.wrapper}>
      <DropdownInput
        options={DemoOptions}
        onChange={setVal}
        value={val}
        triggerPlaceholder={'Select...'}
        withSearchbox
      />
    </div>
  );
};

export const WithError: Story<Props> = () => {
  const [val, setVal] = useState<string | null>('');
  return (
    <div className={classNames.wrapper}>
      <DropdownInput
        options={DemoOptions}
        onChange={setVal}
        value={val}
        icon="CalendarSeasons"
        triggerPlaceholder={'Select...'}
        error={'Some error'}
      />
    </div>
  );
};

export const WithLabel: Story<Props> = () => {
  const [val, setVal] = useState<string | null>('');
  return (
    <div className={classNames.wrapper}>
      <DropdownInput
        options={DemoOptions}
        onChange={setVal}
        value={val}
        triggerPlaceholder={'Select...'}
        label={'Label'}
      />
    </div>
  );
};

export const WithDisabled: Story<Props> = () => {
  const [val, setVal] = useState<string | null>('');
  return (
    <div className={classNames.wrapper}>
      <DropdownInput
        options={[
          { id: '1', label: 'First value' },
          { id: '2', label: 'Second' },
          { id: '3', label: 'I have very long description', disabled: true },
          { id: '4', label: 'Another item' },
        ]}
        onChange={setVal}
        value={val}
        triggerPlaceholder={'Select...'}
      />
    </div>
  );
};

export const WithCategories: Story<Props> = () => {
  const [val, setVal] = useState<string | null>('');
  return (
    <div className={classNames.wrapper}>
      <DropdownInput
        optionGroups={DemoGroups}
        onChange={setVal}
        value={val}
        withSearchbox
        triggerPlaceholder={'Select...'}
        label={'Label'}
      />
    </div>
  );
};

export const WithDescriptions: Story<Props> = () => {
  const [val, setVal] = useState<string | null>('');
  return (
    <div className={classNames.wrapper}>
      <DropdownInput
        options={[
          {
            id: '1',
            label: 'First value',
            description: 'hello, world',
          },
          {
            id: '2',
            label: 'Second',
            description: 'multi\nline\ndescription',
          },
          {
            id: '3',
            label: 'I have very long label',
            description: 'and some text here',
          },
          {
            id: '4',
            label: 'Another item',
            description: 'though I have a very very very very long description',
          },
        ]}
        onChange={setVal}
        value={val}
        triggerPlaceholder="Select..."
      />
    </div>
  );
};
