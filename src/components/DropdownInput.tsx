import { ReactNode } from 'react';
import cx from 'classnames';

import Dropdown from 'components/Dropdown/Dropdown';
import SvgIcon from 'components/SvgIcon';
import FormLabel from 'components/FormLabel';
import { DropdownProps } from 'components/Dropdown/types';

import classNames from './DropdownInput.module.css';

export interface Props extends Omit<DropdownProps, 'renderTrigger'> {
  icon?: string;
  label?: ReactNode;
  error?: ReactNode;
  triggerPlaceholder?: ReactNode;
  renderLoader?: ReactNode;
  isLoading?: boolean;
  disabled?: boolean;
  triggerClassName?: string;
}

function DropdownInput({
  icon,
  label,
  error,
  triggerPlaceholder,
  renderLoader,
  isLoading,
  disabled,
  className,
  triggerClassName,
  ...otherProps
}: Props) {
  return (
    <div className={className}>
      <Dropdown
        {...otherProps}
        renderTrigger={({ toggle, selectedItem }) => (
          <label className={classNames.label}>
            {!!label && <FormLabel>{label}</FormLabel>}
            <div
              className={cx(
                classNames.trigger,
                error && classNames.trigger__error,
                disabled && classNames.disabled,
                triggerClassName && triggerClassName,
              )}
              onClick={(e) => {
                e.stopPropagation();
                if (!disabled) {
                  toggle();
                }
              }}
            >
              {!!icon && <SvgIcon name={icon} width={16} height={16} />}
              <div className={classNames.triggerValue}>
                {isLoading && renderLoader}
                {!isLoading && selectedItem && selectedItem.label}
                {!isLoading && !selectedItem && (
                  <span className={classNames.placeholder}>
                    {triggerPlaceholder}
                  </span>
                )}
              </div>
              <SvgIcon name="Expand" width={16} height={16} />
            </div>
          </label>
        )}
      />
      {!!error && <span className={classNames.error}>{error}</span>}
    </div>
  );
}

export default DropdownInput;
