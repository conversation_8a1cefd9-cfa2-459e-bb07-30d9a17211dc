import { useMemo, useLayoutEffect } from 'react';
import { useMap } from 'react-map-gl';
import mapValues from 'lodash/mapValues';
import { useStore } from 'effector-react';
import { mapLoaded$ } from '../models/map';

// Implement feature-state feature from mapbox gl js, that is missing in react-map-gl library

// WARNING: It only works after map style is loaded, so try not to render it syncrhonously in map children

type Props = {
  sourceId: string;
  sourceLayer?: string;
  featureId: string | number;
  state: object;
};

function FeatureState({ featureId, sourceId, sourceLayer, state }: Props) {
  const isMapLoaded = useStore(mapLoaded$);
  const mapRef = useMap();

  const feature = useMemo(
    () => ({ id: featureId, source: sourceId, sourceLayer }),
    [featureId, sourceId, sourceLayer],
  );

  useLayoutEffect(() => {
    // Map style may not be loaded yet, do nothing in that case
    if (!isMapLoaded) {
      return;
    }
    const map = mapRef.current!.getMap();

    map.setFeatureState(feature, state);

    return () => {
      // Map may be already unloaded by this time, do nothing in that case
      if (!isMapLoaded) {
        return;
      }

      map.setFeatureState(
        feature,
        mapValues(state, () => undefined),
      );
    };
  }, [isMapLoaded, feature, state, mapRef]);

  return null;
}

export default FeatureState;
