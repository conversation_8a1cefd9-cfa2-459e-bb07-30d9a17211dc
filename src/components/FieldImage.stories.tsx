import { styled } from '@linaria/react';
import { Meta, Story } from '@storybook/react';

import FieldImage, { Props } from './FieldImage';
import classNames from './FieldImage.stories.module.css';

import DemoField from 'assets/map/zones_demo_2_field.json';
import DemoFieldImage from 'assets/map/zones_demo_2.png';

export default {
  title: 'Components/FieldImage',
  component: FieldImage,
} as Meta;

const Wrapper = styled.div`
  width: 400px;
  height: 400px;
  margin-left: auto;
  margin-right: auto;
  margin-top: 20px;
`;

export const Default: Story<Props> = () => (
  <Wrapper>
    <FieldImage
      src={DemoFieldImage}
      alt="Demo Field Image"
      geom={DemoField}
      className={classNames.image}
    />
  </Wrapper>
);

export const WithoutSmooth: Story<Props> = () => (
  <Wrapper>
    <FieldImage
      src={DemoFieldImage}
      alt="Demo Field Image"
      geom={undefined}
      className={classNames.image}
    />
  </Wrapper>
);
