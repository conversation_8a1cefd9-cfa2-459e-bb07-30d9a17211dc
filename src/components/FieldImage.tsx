import { feature, Feature, featureCollection, Geometry } from '@turf/helpers';

import GeometryRenderer from 'features/analysis/GeometryRenderer';

const styles = (feature: Feature) => {
  const { properties } = feature;

  // Field with image
  if (properties?.type === 'image') {
    return {
      stroke: 'transparent',
      weight: 1,
    };
  }

  // This should render nothing
  return {};
};

export type Props = {
  src: string;
  alt: string;
  geom: Geometry | undefined;
  className?: string;
};

const FieldImage = ({ src, alt, geom, className }: Props) => {
  if (!geom) {
    return <img className={className} alt={alt} src={src} />;
  }
  return (
    <GeometryRenderer
      className={className}
      styles={styles}
      geojson={featureCollection([
        feature(geom, { type: 'image', imageUrl: src }),
      ])}
    />
  );
};

export default FieldImage;
