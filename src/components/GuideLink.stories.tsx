import GuideLink, { Props } from './GuideLink';
import { styled } from '@linaria/react';
import { <PERSON><PERSON>, <PERSON> } from '@storybook/react';

export default {
  title: 'Components/GuideLink',
  component: GuideLink,
} as Meta;

const Wrapper = styled.div`
  width: 400px;
  height: 400px;
  margin-left: auto;
  margin-right: auto;
  margin-top: 20px;
`;

export const Default: Story<Props> = () => (
  <Wrapper>
    <GuideLink url="http://google.com">
      Learn more about our guide links
    </GuideLink>
  </Wrapper>
);
