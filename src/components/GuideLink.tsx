import { ReactNode } from 'react';

import SvgIcon from 'components/SvgIcon';

import { eventLogged } from 'models/analytics';

import classNames from './GuideLink.module.css';

/* eslint-disable react/jsx-no-target-blank */

export type Props = {
  url: string;
  children: ReactNode;
};

function GuideLink({ url, children }: Props) {
  return (
    <a
      className={classNames.container}
      href={url}
      target="_blank"
      onClick={() => {
        eventLogged({
          name: 'yield_guide_open',
          params: { from: 'vra_form' },
        });
      }}
    >
      <SvgIcon
        className={classNames.icon}
        name="Guide"
        width={16}
        height={16}
      />
      {children}
    </a>
  );
}

export default GuideLink;
