import { forwardRef, ForwardedRef } from 'react';
import { NavLink, NavLinkProps, Path } from 'react-router-dom';
import { useStore } from 'effector-react';
import pick from 'lodash/pick';
import qs from 'qs';

import { debouncedViewState$ } from 'models/map';
import {
  MapCoordsQueryKey,
  MapZoomQueryKey,
  CoordinateStoragePrecision,
} from 'constants/storage';

const PreserveQueryKeys = [MapCoordsQueryKey, MapZoomQueryKey];

type LinkProps = NavLinkProps & {
  withMapSync?: boolean;
  disabled?: boolean;
};

// this link saves query for coords and zoom
const Link = forwardRef(
  (
    { disabled = false, withMapSync = true, ...props }: LinkProps,
    ref: ForwardedRef<HTMLAnchorElement>,
  ) => {
    const { latitude, longitude, zoom } = useStore(debouncedViewState$);

    let target: Partial<Path>;
    const { to } = props;

    if (typeof to === 'string') {
      target = { pathname: to };
    } else {
      target = to;
    }

    // using debouncedViewState$ effector store
    // instead of window.location.search because of delay
    const prevQuery = qs.parse(
      qs.stringify({
        [MapCoordsQueryKey]: [longitude, latitude]
          .map((v) => v.toFixed(CoordinateStoragePrecision))
          .join(':'),
        [MapZoomQueryKey]: zoom.toFixed(2),
      }),
    );

    const targetQuery = qs.parse((target.search || '').slice(1));
    const pathWithQuery: Partial<Path> = {
      ...target,
      search: qs.stringify({
        ...(withMapSync ? pick(prevQuery, PreserveQueryKeys) : {}),
        ...targetQuery,
      }),
    };

    return (
      <NavLink
        {...props}
        ref={ref}
        to={pathWithQuery}
        onClick={disabled ? (e) => e.preventDefault() : props.onClick}
      />
    );
  },
);

export default Link;
