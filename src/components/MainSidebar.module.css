.header {
  width: 100%;
  position: relative;
}

.logo {
  padding: 8px;
  position: relative;
}

.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.topBlock {
  padding: 8px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.footer {
  position: relative;
  display: flex;
  flex-direction: column;
  padding: 8px;
  gap: 8px;
}

.sidebarButton {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  color: var(--color-black);
  background-color: transparent;
  padding: 0;
  border: none;
  position: relative;
}

.sidebarButton:hover {
  color: var(--color-black);
  background-color: var(--color-grey-50);
}

.sidebarButton:global(.active) {
  background-color: var(--color-grey-50);
}

.sidebar {
  height: 100%;
  display: flex;
  flex-shrink: 0;
  background-color: var(--color-grey-30);
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  width: 56px;
  padding-top: 8px;
}

.gearWrap {
  position: absolute;
  padding: 2px;
  top: -8px;
  right: -4px;
  width: 16px;
  height: 16px;
  background-color: var(--color-grey-30);
  border-radius: 4px;
}
