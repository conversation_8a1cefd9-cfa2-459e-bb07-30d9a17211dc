import { useState, useMemo } from 'react';
import { useStore } from 'effector-react';
import { useTranslation } from 'react-i18next';
import cx from 'classnames';

import Link from 'components/Link';
import SvgIcon from 'components/SvgIcon';
import IconButton from 'components/IconButton/IconButton';
import { Hint } from 'components/Tooltip/Hint';
import Popover, { NavBox } from 'components/Popover/Popover';
import { Seasons } from 'features/field-list/Seasons';
import SettingsMenu from 'features/field-list/SettingsMenu';
import MobileMenu from 'features/field-list/MobileMenu';
import Logo from 'features/field-list/Logo';
import { SearchModal } from 'features/field-list/SearchModal';
import DocumentKeyHandler from 'components/DocumentKeyHandler/DocumentKeyHandler';

import { eventLogged } from 'models/analytics';
import { debouncedViewState$ } from 'models/map';
import { hasModems$ } from 'models/modems';
import { hasStations$ } from 'models/stations';
import { currentSeason$ } from 'models/seasons';
import { importStageSet } from 'features/import-files/models/import';
import { isDemoAccount$ } from 'features/demo-account/models/demo-account';
import { currentMembership$ } from 'features/multiaccount/models/multiaccount';
import { searchRequested } from 'features/field-list/models/search';
import { uploadRestrictedRoles } from 'features/import-files/models/upload-page';

import { getScoutingUrl } from 'utils/scouting';
import config from 'config';

import classNames from './MainSidebar.module.css';

export const MainSidebar = () => {
  const { i18n, t } = useTranslation();
  const [isProfileOpen, setIsProfileOpen] = useState(false);
  const [isMobileOpen, setIsMobileOpen] = useState(false);
  const isDemoAccount = useStore(isDemoAccount$);
  const currentMembership = useStore(currentMembership$);
  const currentSeason = useStore(currentSeason$);
  const viewState = useStore(debouncedViewState$);
  const hasModems = useStore(hasModems$);
  const hasStations = useStore(hasStations$);
  const isRestricted =
    currentMembership && uploadRestrictedRoles.includes(currentMembership.role);

  const uploadHint = useMemo(() => {
    if (isDemoAccount) {
      return t('demo-account.feature-disabled');
    }
    if (isRestricted) {
      return t('multiaccount.role.feature-disabled');
    }
    return t('sidebar.upload-data');
  }, [isRestricted, isDemoAccount, t]);

  const demoAccountHint = (message: string) => {
    if (isDemoAccount) {
      return t('demo-account.feature-disabled');
    } else {
      return message;
    }
  };

  return (
    <aside className={classNames.sidebar}>
      <SearchModal />
      <DocumentKeyHandler
        hotkey="mod+K"
        onPress={() => {
          searchRequested();
        }}
      />

      <div className={classNames.header}>
        <div className={classNames.logo}>
          <Logo />
        </div>

        {!config.whiteLabel?.hideSeasons && (
          <div className={classNames.logo}>
            <Seasons />
          </div>
        )}

        <div className={classNames.topBlock}>
          {/*<Hint content={t('sidebar.search')}>
            <IconButton
              className={classNames.sidebarButton}
              dataTestId="#menu_search"
              icon="Search"
              iconSize={24}
              onClick={() => {
                searchRequested();
              }}
            />
          </Hint>*/}

          <div>
            <Hint content={t('sidebar.fields-and-vra')}>
              <div>
                <Link
                  to="/"
                  data-testid="#menu_fields_maps"
                  className={classNames.sidebarButton}
                >
                  <SvgIcon name={'Fields'} width={24} height={24} />
                </Link>
              </div>
            </Hint>
          </div>

          {!config.whiteLabel?.hidePlatform && (
            <Hint content={demoAccountHint(t('sidebar.crop-rotation'))}>
              <a
                href={getScoutingUrl(
                  i18n,
                  'crop-rotation',
                  viewState,
                  currentSeason?.id,
                  currentMembership?.workspace_id,
                )}
                onClick={(e) => {
                  isDemoAccount && e.preventDefault();
                }}
                className={cx(
                  classNames.sidebarButton,
                  isDemoAccount && classNames.disabled,
                )}
                data-testid="#menu_crop-rotation"
              >
                <SvgIcon name="CropRotation" width={24} height={24} />
              </a>
            </Hint>
          )}

          {!config.whiteLabel?.hidePlatform && (
            <Hint content={demoAccountHint(t('sidebar.scouting'))}>
              <a
                href={getScoutingUrl(
                  i18n,
                  'scouting',
                  viewState,
                  currentSeason?.id,
                  currentMembership?.workspace_id,
                )}
                onClick={(e) => {
                  isDemoAccount && e.preventDefault();
                }}
                className={cx(
                  classNames.sidebarButton,
                  isDemoAccount && classNames.disabled,
                )}
                data-testid="#menu_notes"
              >
                <SvgIcon name="Notes" width={24} height={24} />
              </a>
            </Hint>
          )}

          <Hint content={uploadHint}>
            <IconButton
              className={cx(
                classNames.sidebarButton,
                (isDemoAccount || isRestricted) && classNames.disabled,
              )}
              dataTestId="#menu_upload"
              icon="Upload"
              iconSize={24}
              onClick={() => {
                if (isDemoAccount || isRestricted) {
                  return;
                }
                importStageSet();
              }}
            />
          </Hint>

          {!config.whiteLabel?.hidePlatform && (
            <Hint content={demoAccountHint(t('sidebar.files'))}>
              <a
                href={getScoutingUrl(
                  i18n,
                  'files',
                  viewState,
                  currentSeason?.id,
                  currentMembership?.workspace_id,
                )}
                onClick={(e) => {
                  isDemoAccount && e.preventDefault();
                }}
                className={cx(
                  classNames.sidebarButton,
                  isDemoAccount && classNames.disabled,
                )}
                data-testid="#menu_files"
              >
                <SvgIcon name="FieldData" width={24} height={24} />
              </a>
            </Hint>
          )}

          {!config.whiteLabel?.hidePlatform && hasModems && (
            <Hint content={t('sidebar.modems')}>
              <a
                href={getScoutingUrl(
                  i18n,
                  'modems',
                  viewState,
                  currentSeason?.id,
                  currentMembership?.workspace_id,
                )}
                className={classNames.sidebarButton}
                data-testid="#menu_modems"
              >
                <SvgIcon name="Modem" width={24} height={24} />
              </a>
            </Hint>
          )}

          {!config.whiteLabel?.hidePlatform && hasStations && (
            <Hint content={demoAccountHint(t('sidebar.stations'))}>
              <a
                href={getScoutingUrl(
                  i18n,
                  'stations',
                  viewState,
                  currentSeason?.id,
                  currentMembership?.workspace_id,
                )}
                onClick={(e) => {
                  isDemoAccount && e.preventDefault();
                }}
                className={cx(
                  classNames.sidebarButton,
                  isDemoAccount && classNames.disabled,
                )}
                data-testid="#menu_stations"
              >
                <SvgIcon name="Sensor" width={24} height={24} />
              </a>
            </Hint>
          )}
        </div>
      </div>

      <div className={classNames.footer}>
        {!config.whiteLabel?.hideYieldGuide && (
          <Hint content={t('sidebar.new-releases')}>
            <a
              href={t('urls.new-releases')}
              target="_blank"
              rel="noreferrer"
              className={classNames.sidebarButton}
              data-testid="#menu_new-releases"
              onClick={() => {
                eventLogged({
                  name: 'yield_new_releases_open',
                  params: { from: 'sidebar' },
                });
              }}
            >
              <SvgIcon name={'NewReleasesSidebar'} width={24} height={24} />
            </a>
          </Hint>
        )}
        {!config.whiteLabel?.hideYieldGuide && (
          <Hint content={t('sidebar.used-guide')}>
            <a
              href={t('urls.guide')}
              target="_blank"
              rel="noreferrer"
              className={classNames.sidebarButton}
              data-testid="#menu_guide"
              onClick={() => {
                eventLogged({
                  name: 'yield_guide_open',
                  params: { from: 'sidebar' },
                });
              }}
            >
              <SvgIcon name="Guide" width={24} height={24} />
            </a>
          </Hint>
        )}
        {(!config.whiteLabel?.mobile ||
          config.whiteLabel.mobile.ios ||
          config.whiteLabel.mobile.android) && (
          <Popover
            trigger={'click'}
            interactive
            offset={[0, 4]}
            onShow={() => setIsMobileOpen(true)}
            onHide={() => setIsMobileOpen(false)}
            content={<MobileMenu />}
            placement="right-end"
          >
            <div>
              <Hint disabled={isMobileOpen} content={t('sidebar.download')}>
                <div>
                  <IconButton
                    className={classNames.sidebarButton}
                    icon="Mobile"
                    hitSlop={0}
                    dataTestId="#menu_mobile"
                  />
                </div>
              </Hint>
            </div>
          </Popover>
        )}
        <Popover
          trigger={'click'}
          interactive
          offset={[0, 4]}
          onShow={() => setIsProfileOpen(true)}
          onHide={() => setIsProfileOpen(false)}
          render={(attrs) => {
            return (
              <NavBox {...attrs}>
                <SettingsMenu />
              </NavBox>
            );
          }}
          placement="top"
        >
          <div>
            <Hint disabled={isProfileOpen} content={t('sidebar.profile')}>
              <div>
                <IconButton
                  className={classNames.sidebarButton}
                  icon="User"
                  hitSlop={0}
                  dataTestId="#menu_settings"
                />
              </div>
            </Hint>
          </div>
        </Popover>
      </div>
    </aside>
  );
};

export default MainSidebar;
