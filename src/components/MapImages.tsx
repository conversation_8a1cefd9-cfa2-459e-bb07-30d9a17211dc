import { useLayoutEffect } from 'react';
import { useMap } from 'react-map-gl';

type Image = {
  url: string;
  contentBox?: number[];
  stretchX?: [number[]];
  stretchY?: [number[]];
  pixelRatio: number;
};

type Props = {
  images: { [imageId: string]: Image };
};

function MapImages({ images }: Props) {
  const { current: map } = useMap();

  useLayoutEffect(() => {
    if (!map) {
      return;
    }

    for (const [imageId, meta] of Object.entries(images)) {
      if (map.hasImage(imageId)) {
        continue;
      }

      map.loadImage(meta.url, (error?: Error, image?: any) => {
        if (error) {
          return;
        }
        if (map.hasImage(imageId)) {
          return;
        }
        map.addImage(imageId, image, {
          content: meta.contentBox,
          stretchX: meta.stretchX,
          stretchY: meta.stretchY,
          pixelRatio: meta.pixelRatio,
        } as any);
      });
    }
  }, [map, images]);

  return null;
}

export default MapImages;
