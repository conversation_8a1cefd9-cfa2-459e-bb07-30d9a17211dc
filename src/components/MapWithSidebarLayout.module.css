.container {
  height: 100%;
  min-height: 600px;
  position: relative;
  display: flex;
}

.map {
  width: 100%;
  height: 100%;
  position: relative;
  background: black;
}

.content-wrapper {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 2;
  margin: 8px;
  border-radius: 12px;
  overflow: hidden;

  transition: transform 0.3s;
  transform: translateX(0);
}

:global(.sidebarExpanded) .content-wrapper {
  transform: translateX(var(--folders-width));
  right: var(--folders-width);
  transition: transform 0.3s, right 0ms 0.3s;
}

.content {
  background: white;
  height: 100%;
  position: relative;
  overflow-y: auto;
}

.toolbar {
  position: absolute;
  display: flex;
  justify-content: space-between;
  left: 15px;
  top: 15px;
  right: 15px;
  height: 0;
}

.legend {
  position: absolute;
  display: flex;
  justify-content: space-between;
  left: -8px;
  bottom: 8px;
}
