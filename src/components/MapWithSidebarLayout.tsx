import { useEffect } from 'react';
import { Outlet, useLocation } from 'react-router-dom';
import { useStore, useStoreMap } from 'effector-react';

import {
  isMapDrawModeEnabled$,
  mapDraw$,
  mapDrawModeEnabledSet,
} from '../features/map-measure/models/map-measure';
import { useOutsideClickTrigger } from '../utils/use-outside-click';
import { slotsContent$ } from 'models/slots';

import Slot from 'components/Slot';
import { MapView } from 'components/MapView';
import DebugTileGridLayer from 'components/DebugTileGridLayer';
import MapAnimationLayer from 'features/map/MapAnimationLayer';
import PositionTo from './PositionTo/PositionTo';
import TerrainLayer from './TerrainLayer/TerrainLayer';
import MapMeasurePanel from '../features/map-measure/MapMeasurePanel/MapMeasurePanel';

import classNames from './MapWithSidebarLayout.module.css';

// I wanted to use Symbol() here, but react does not support symbols as keys
// https://github.com/facebook/react/issues/4847

export const MapLayersSlot = 'map-layers';
export const MapOverlaysSlot = 'map-overlays';
export const MapToolbarSlot = 'toolbar';
export const ContentSlot = 'content';
export const OverlaysSlot = 'overlays';

function MapWithSidebarLayout() {
  const { pathname } = useLocation();
  const contentItems = useStoreMap(
    slotsContent$,
    (content) => content[ContentSlot],
  );
  const isMapDrawModeEnabled = useStore(isMapDrawModeEnabled$);
  const mapDraw = useStore(mapDraw$);
  useEffect(() => {
    mapDrawModeEnabledSet(false);
  }, [pathname]);

  const { ref } = useOutsideClickTrigger<HTMLDivElement>(() => {
    if (isMapDrawModeEnabled) {
      mapDraw?.changeMode('simple_select');
    }
  });

  return (
    <>
      <div ref={ref} className={classNames.map}>
        <MapView id="map-with-sidebar">
          <DebugTileGridLayer />
          <MapAnimationLayer />
          <TerrainLayer />
          <Slot id={MapLayersSlot} />
        </MapView>

        <div className={classNames.toolbar}>
          <Slot id={MapToolbarSlot} exclusive />
        </div>

        {!!contentItems && contentItems.length > 0 && (
          <div className={classNames['content-wrapper']}>
            <div className={classNames.content}>
              <Slot id={ContentSlot} exclusive />
            </div>
          </div>
        )}

        <Slot id={MapOverlaysSlot} />

        <PositionTo placement="center bottom" offsetY={10} withSidebarSync>
          {isMapDrawModeEnabled && <MapMeasurePanel />}
        </PositionTo>
      </div>

      <Slot id={OverlaysSlot} />

      {/* We expect this to not render anything directly */}
      <Outlet />
    </>
  );
}

export default MapWithSidebarLayout;
