import merge from 'lodash/merge';

import { ConfigOverridesKey } from 'constants/storage';

// TODO: Find a way to conditionally include those files in a bundle,
// to not leak dev config in prod files
import LocalConfig from './local.json';
import DevelopmentConfig from './development.json';
import StagingConfig from './staging.json';
import ProductionConfig from './production.json';

import { Config } from './types';

let config: Config;

if (import.meta.env.MODE === 'local-dev') {
  config = LocalConfig;
} else if (import.meta.env.MODE === 'development') {
  config = DevelopmentConfig;
} else if (import.meta.env.MODE === 'staging') {
  config = StagingConfig;
} else {
  config = ProductionConfig;
}

const domainOverrides = config.domainOverrides[window.location.host];
if (domainOverrides) {
  merge(config, domainOverrides);
}

if (localStorage[ConfigOverridesKey]) {
  try {
    const overrides = JSON.parse(localStorage[ConfigOverridesKey]);
    merge(config, overrides);
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('Failed to load config overrides', error);
  }
}

export default config;
