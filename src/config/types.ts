// Utility type from stackoverflow
type DeepPartial<T> = T extends object
  ? { [P in keyof T]?: DeepPartial<T[P]> }
  : T;

export type Config = {
  platformAppUrl: string;
  apiHost: string;
  iotApiHost: string;
  whiteLabel?: {
    logo: string;
    title?: string;
    favicon?: string;
    className?: string;
    forceSeasonName?: string;
    forceProEmail?: string;
    hideSeasons?: boolean;
    hidePlatform?: boolean; // hide link to platform
    allowConsultants?: boolean; // but allow consultants
    hideChargingRules?: boolean;
    hideYieldGuide?: boolean; // hide guide and changelog
    hideYieldPro?: boolean; // hide pro functionality
    hideIntercom?: boolean; // hide intercom
    readOnly?: boolean;
    redirectUrl?: string;
    mobile?: {
      ios?: string | null;
      android?: string | null;
    };
  };
  features: {
    showDebugLanguage?: boolean;
  };
  apiKeys: {
    firebase: string;
    mapbox: string;
    sentry?: string;
  };
  appIDs: {
    firebase: string;
    firebaseProjectID: string;
    gtag: string;
    intercom: string;
  };
  domainOverrides: {
    [domain: string]: DeepPartial<Config>;
  };
};
