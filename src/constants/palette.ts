import chroma from 'chroma-js';

export const ZonesUndefined = '#D1D1D6'; // --color-grey-50

const ZonesP01 = '#F8D2FC';
const ZonesP02 = '#E5B5E9';
const ZonesP03 = '#D499D7';
const ZonesP04 = '#C27DC5';
const ZonesP05 = '#A85BA8';
const ZonesP06 = '#9D419C';
const ZonesP07 = '#8F1B8C';
export const VRAZoneColors: { [count: number]: string[] } = {
  3: [ZonesP01, ZonesP04, ZonesP07],
  5: [ZonesP01, ZonesP03, ZonesP04, ZonesP05, ZonesP07],
  7: [ZonesP01, ZonesP02, ZonesP03, ZonesP04, ZonesP05, ZonesP06, ZonesP07],
};

const ZonesN01 = '#D0E3FC';
const ZonesN02 = '#C1DAFB';
const ZonesN03 = '#A5CBF8';
const ZonesN04 = '#94C1F7';
const ZonesN05 = '#79B0F1';
const ZonesN06 = '#61A2EF';
const ZonesN07 = '#4392F1';
export const VegetationZoneColors: { [count: number]: string[] } = {
  3: [ZonesN01, ZonesN04, ZonesN07],
  5: [ZonesN01, ZonesN03, ZonesN04, ZonesN05, ZonesN07],
  7: [ZonesN01, ZonesN02, ZonesN03, ZonesN04, ZonesN05, ZonesN06, ZonesN07],
};

const ZonesU01 = '#BEFFDA';
const ZonesU02 = '#8FECBB';
const ZonesU03 = '#78E3AC';
const ZonesU04 = '#60D99C';
const ZonesU05 = '#49D08D';
const ZonesU06 = '#31C67D';
const ZonesU07 = '#02B35D';
export const SSResultZoneColors: { [count: number]: string[] } = {
  2: [ZonesU01, ZonesU07],
  3: [ZonesU01, ZonesU04, ZonesU07],
  4: [ZonesU01, ZonesU03, ZonesU05, ZonesU07],
  5: [ZonesU01, ZonesU03, ZonesU04, ZonesU05, ZonesU07],
  6: [ZonesU01, ZonesU03, ZonesU04, ZonesU05, ZonesU06, ZonesU07],
  7: [ZonesU01, ZonesU02, ZonesU03, ZonesU04, ZonesU05, ZonesU06, ZonesU07],
};

export const NDVIColors: Record<string, string> = {
  0: '#300B05',
  6.9: '#812C14',
  12.5: '#B36127',
  18.49: '#E7BC4C',
  24.48: '#F3DA60',
  30.4: '#F8EE64',
  36.56: '#F4F550',
  43.29: '#D1E54A',
  50: '#A3CF40',
  62.9: '#4D8F28',
  78.35: '#1E4816',
  100: '#123011',
};

export const NDVIContrastedColors: Record<string, string> = {
  0: '#881E4C',
  14.78: '#DB3D25',
  34.28: '#F1B54F',
  42.89: '#F7E050',
  50: '#EFF651',
  68.68: '#80DB45',
  85.94: '#4AA069',
  93.27: '#3E8874',
  100: '#3D8575',
};

export const ElevationMapColors: Record<string, string> = {
  0: '#491E3C',
  50: '#D8572E',
  100: '#F0D893',
};

export const SoilBrightnessMapColors: Record<string, string> = {
  0: '#D2DC51',
  50: '#6FAA7B',
  100: '#402464',
};

export const YieldValueMapColors: Record<string, string> = {
  0: '#0c8e44',
  9.1: '#0a8451',
  18.2: '#077b5c',
  27.3: '#05706c',
  36.4: '#046878',
  45.5: '#026181',
  54.5: '#055d8d',
  63.7: '#23709b',
  72.8: '#4284a9',
  81.9: '#5792b3',
  90: '#73a4bf',
  100: '#8bb4ca',
};

export const SowingDateMapColors: Record<string, string> = {
  0: '#1B5800',
  100: '#FEFFC0',
};

export const HarvestDateMapColors: Record<string, string> = {
  0: '#D24B1D',
  100: '#FFF5DA',
};

export const HeterogenityMapColors: Record<string, string> = {
  0: '#C50465',
  12.5: '#EE0200',
  25: '#FF771C',
  37.5: '#FFBD38',
  50: '#FEFF03',
  62.5: '#BDF100',
  75: '#3FE10E',
  87.5: '#07B55E',
  100: '#0B8877',
};

export const AverageNdviMapColors: Record<string, string> = {
  0: '#15442D',
  5.9: '#15542D',
  11.8: '#25602D',
  17.6: '#2D7000',
  23.5: '#458100',
  29.4: '#5B8E03',
  35.3: '#72A000',
  41.2: '#8AAF00',
  47: '#A2C000',
  52.9: '#B9CF02',
  58.8: '#D0DF00',
  64.7: '#E6EC06',
  70.5: '#FDFE03',
  76.4: '#E6C957',
  82.3: '#C6974E',
  88.2: '#B76135',
  94: '#7F4020',
  100: '#422112',
};

export const WaterMapColors: Record<string, string> = {
  0: '#D5F2FF',
  10: '#BEE7FF',
  20: '#A7DCFF',
  30: '#90D1FF',
  40: '#78C5FF',
  50: '#66B4FD',
  60: '#53A3FA',
  70: '#4092F8',
  80: '#2D80F5',
  90: '#1972EE',
  100: '#0D65E8',
};

export const ChlorophyllMapColors: Record<string, string> = {
  0: '#FFFBC5',
  10: '#E9F4BD',
  20: '#C8E8AF',
  30: '#A6DCA1',
  40: '#85D093',
  50: '#63C384',
  60: '#5AB478',
  70: '#50A46C',
  80: '#3F955C',
  90: '#2C874A',
  100: '#1B7A3A',
};

// 0-20-35-50-65-80-100
const SoilSamplingResultMilestonePercents = [20, 35, 50, 65, 80];
export const SoilSamplingResultColorPercents = (
  minValue: number,
  maxValue: number,
) => {
  const milestones: number[] = [];
  SoilSamplingResultMilestonePercents.forEach((percent) => {
    const value = minValue + (maxValue - minValue) * (percent / 100);
    milestones.push(value);
  });
  return milestones;
};
export const SoilSamplingResultDefaultColors: Record<string, string> = {
  0: '#773c1d',
  20: '#da5947',
  40: '#e4a04e',
  60: '#f9e972',
  80: '#70a84e',
  100: '#43703f',
};

export const ElectroConductivityDefaultColors: Record<string, string> = {
  0: '#e2d47f',
  10: '#e0c580',
  20: '#deb680',
  30: '#d99881',
  40: '#d57a82',
  50: '#d05c83',
  60: '#b34d83',
  70: '#953d83',
  80: '#772d83',
  90: '#682583',
  100: '#591d82',
};

export const sortArrayByKey = (
  palette: Record<string, string>,
  reverted?: boolean,
) => {
  if (!palette) return [];
  return Object.entries(palette)
    .sort(([a], [b]) =>
      reverted ? parseFloat(b) - parseFloat(a) : parseFloat(a) - parseFloat(b),
    )
    .map(([k, v]) => v);
};

export const generateGradient = (
  palette: Record<string, string>,
  deg: number = 0,
) => {
  const colors = sortArrayByKey(palette).map(
    (v, index) =>
      `${v} ${(index / (Object.entries(palette).length - 1)) * 100}%`,
  );
  return `linear-gradient(${deg}deg, ${colors.join(', ')})`;
};

export const getPaletteColorByValue = (
  value: number,
  palette = NDVIContrastedColors,
) => {
  const colors = Object.entries(palette)
    .sort((a, b) => +a[0] - +b[0])
    .map((p) => p[1]);
  const range = Object.keys(palette)
    .map((val) => +val)
    .sort((a, b) => a - b);
  return getPaletteColorByValueWithRange(value, range, colors);
};

export const getPaletteColorByValueWithRange = (
  value: number,
  range: number[],
  colors: string[],
) => chroma.scale(colors).domain(range)(value);
