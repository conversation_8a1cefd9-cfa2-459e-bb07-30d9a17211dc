import { FillingLayers, VRAFillingLayers } from 'models/fieldSeasons';
import { SoilSamplingSegmentation } from '../features/soil-sampling/models/types';

export const SoilSamplingQualityTypes = ['low', 'medium', 'high'] as const;
export const soilSamplingQualities: {
  [key in typeof SoilSamplingQualityTypes[number]]: {
    range: [number, number];
    color: string;
  };
} = {
  low: {
    range: [0, 0.85],
    color: '#FF3A20',
  },
  medium: {
    range: [0.85, 0.9],
    color: '#FF953D',
  },
  high: {
    range: [0.9, 1],
    color: '#81D63C',
  },
};

export const getSoilSamplingMapZoneColor = (
  properties: SoilSamplingSegmentation['features'][number]['properties'],
) => {
  const { quality } = properties;
  if (!quality) return 'var(--color-grey-30)';
  if (
    quality > soilSamplingQualities.low.range[0] &&
    quality < soilSamplingQualities.low.range[1]
  )
    return soilSamplingQualities.low.color;
  if (
    quality >= soilSamplingQualities.medium.range[0] &&
    quality <= soilSamplingQualities.medium.range[1]
  )
    return soilSamplingQualities.medium.color;
  if (
    quality > soilSamplingQualities.high.range[0] &&
    quality <= soilSamplingQualities.high.range[1]
  )
    return soilSamplingQualities.high.color;
};

export const SoilSamplingFillings = [
  FillingLayers.ProductivityMap,
  VRAFillingLayers.ProductivityMap,
] as const;
export type SoilSamplingFilling = typeof SoilSamplingFillings[number];
