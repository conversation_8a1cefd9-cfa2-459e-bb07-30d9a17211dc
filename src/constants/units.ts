import { AllMeasuresUnits } from 'utils/convert-units';

export type UnitCategory =
  | 'area'
  | 'elevation'
  | 'temperature'
  | 'precipitation'
  | 'speed'
  | 'yieldMass'
  | 'seedsTotal'
  | 'massTotal'
  | 'volumeTotal';
export type UnitSystem = 'metric' | 'imperial' | 'hybrid';

export type DoubleUnit = `${AllMeasuresUnits}/${AllMeasuresUnits}`;

type UnitCategoriesType = {
  [unit in UnitCategory]: {
    [unitSystem in UnitSystem]: {
      unit: AllMeasuresUnits;
      precision: number;
    };
  };
};

export const Units: UnitSystem[] = ['metric', 'imperial', 'hybrid'];

export const UnitCategories: UnitCategoriesType = {
  area: {
    metric: {
      unit: 'ha',
      precision: 1,
    },
    imperial: {
      unit: 'ac',
      precision: 1,
    },
    hybrid: {
      unit: 'ac',
      precision: 1,
    },
  },
  elevation: {
    metric: {
      unit: 'm',
      precision: 0,
    },
    imperial: {
      unit: 'ft',
      precision: 0,
    },
    hybrid: {
      unit: 'ft',
      precision: 0,
    },
  },
  temperature: {
    metric: {
      unit: 'C',
      precision: 0,
    },
    imperial: {
      unit: 'F',
      precision: 0,
    },
    hybrid: {
      unit: 'C',
      precision: 0,
    },
  },
  precipitation: {
    metric: {
      unit: 'mm',
      precision: 0,
    },
    imperial: {
      unit: 'in',
      precision: 0,
    },
    hybrid: {
      unit: 'mm',
      precision: 0,
    },
  },
  speed: {
    metric: {
      unit: 'm/s',
      precision: 0,
    },
    imperial: {
      unit: 'mph',
      precision: 0,
    },
    hybrid: {
      unit: 'km/h',
      precision: 0,
    },
  },
  yieldMass: {
    metric: {
      unit: 'mt',
      precision: 2,
    },
    imperial: {
      unit: 'lb',
      precision: 0,
    },
    hybrid: {
      unit: 'lb',
      precision: 0,
    },
  },
  massTotal: {
    metric: {
      unit: 'kg',
      precision: 1,
    },
    imperial: {
      unit: 'lb',
      precision: 1,
    },
    hybrid: {
      unit: 'lb',
      precision: 1,
    },
  },
  volumeTotal: {
    metric: {
      unit: 'l',
      precision: 1,
    },
    imperial: {
      unit: 'gal',
      precision: 1,
    },
    hybrid: {
      unit: 'gal',
      precision: 1,
    },
  },

  seedsTotal: {
    metric: {
      unit: 'seeds',
      precision: 0,
    },
    imperial: {
      unit: 'seeds',
      precision: 0,
    },
    hybrid: {
      unit: 'seeds',
      precision: 0,
    },
  },
};
