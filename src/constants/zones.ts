import invert from 'lodash/invert';
import mapValues from 'lodash/mapValues';

import { UnitSystem, DoubleUnit } from 'constants/units';
import { FillingLayers, VRAFillingLayers } from 'models/fieldSeasons';
import { ZoneName, VRAMapInputType } from 'models/vraMaps';

export enum VRAFormMode {
  View = 'view',
  Edit = 'edit',
  Create = 'create',
}

// TODO: Make enum and probably get rid of filling types
export const ZoneFillings = [
  VRAFillingLayers.ProductivityMap,
  VRAFillingLayers.Ndvi,
  VRAFillingLayers.SoilSamplingResult,
  FillingLayers.ProductivityMap,
  FillingLayers.Ndvi,
  FillingLayers.ContrastedNdvi,
  FillingLayers.SatelliteImage,
  FillingLayers.Ndmi,
  FillingLayers.Smi,
  FillingLayers.Ndre,
  FillingLayers.Msavi,
  FillingLayers.Reci,
  FillingLayers.Ndwi,
  FillingLayers.Pri,
  FillingLayers.Mcari,
] as const;
export type ZoneFilling = typeof ZoneFillings[number];

export const ZonesDifferenceRates: { [count: string]: number } = {
  '+30%': 1.3,
  '+20%': 1.2,
  '+15%': 1.15,
  '+10%': 1.1,
  '+5%': 1.05,
  Standard: 1.0,
  '-5%': 0.95,
  '-10%': 0.9,
  '-15%': 0.85,
  '-20%': 0.8,
  '-30%': 0.7,
};

export const UndefinedZoneIndex = 404;

export const ZoneNamesByIndex: { [index: number]: ZoneName } = {
  [-3]: 'm3',
  [-2]: 'm2',
  [-1]: 'm1',
  0: 'p0',
  1: 'p1',
  2: 'p2',
  3: 'p3',
  [UndefinedZoneIndex]: 'undefined', // for undefined zone
};

export const ZoneIndexesByName: { [name in ZoneName]: number } = mapValues(
  invert(ZoneNamesByIndex),
  (item) => Number(item),
) as any;

export enum SprayingProtectionMode {
  Desiccants = 'desiccants',
  Fungicides = 'fungicides',
  GrowthRegulators = 'growth_regulators',
  Herbicides = 'herbicides',
  Insecticides = 'insecticides',
  NitrogenStabilizer = 'nitrogen_stabilizer',
  Bioinputs = 'bioinputs',
}

export enum JDChemicalType {
  ADDITIVE = 'ADDITIVE',
  FUNGICIDE = 'FUNGICIDE',
  INSECTICIDE = 'INSECTICIDE',
  HERBICIDE = 'HERBICIDE',
  GROWTH_REGULATOR = 'GROWTH_REGULATOR',
  NITROGEN_STABILIZER = 'NITROGEN_STABILIZER',
  ALL = 'all',
}

export const SprayingProtectionModeMapper: Record<
  SprayingProtectionMode,
  JDChemicalType
> = {
  [SprayingProtectionMode.Desiccants]: JDChemicalType.ADDITIVE,
  [SprayingProtectionMode.Fungicides]: JDChemicalType.FUNGICIDE,
  [SprayingProtectionMode.GrowthRegulators]: JDChemicalType.GROWTH_REGULATOR,
  [SprayingProtectionMode.Herbicides]: JDChemicalType.HERBICIDE,
  [SprayingProtectionMode.Insecticides]: JDChemicalType.INSECTICIDE,
  [SprayingProtectionMode.NitrogenStabilizer]:
    JDChemicalType.NITROGEN_STABILIZER,
  [SprayingProtectionMode.Bioinputs]: JDChemicalType.ALL,
};

export const ZonesCount = [3, 5, 7];
export const AllZonesCount = [2, 3, 4, 5, 6, 7];

export enum ZonesError {
  Default = 'default',
  NoSoilSamplingZones = 'no-soil-sampling-zones',
}

// It is important to keep number of items the same in these arrays
// This is required to allow automatically converting them to different unit system

export enum MaterialClassification {
  Liquid = 'LIQUID',
  Dry = 'DRY',
}

export const UnitsToMaterialClassification: Partial<
  Record<DoubleUnit, MaterialClassification>
> = {
  'l/ha': MaterialClassification.Liquid,
  'gal/ac': MaterialClassification.Liquid,
  'kg/ha': MaterialClassification.Dry,
  'lb/ac': MaterialClassification.Dry,
};

export const RateUnitsByType: {
  [vraType in VRAMapInputType]: {
    [unitSystem in UnitSystem]: DoubleUnit[];
  };
} = {
  sowing: {
    metric: ['seeds/ha', 'kg/ha'],
    imperial: ['seeds/ac', 'lb/ac'],
    hybrid: ['seeds/ac', 'lb/ac'],
  },
  spraying: {
    metric: ['kg/ha', 'l/ha'],
    imperial: ['lb/ac', 'gal/ac'],
    hybrid: ['lb/ac', 'gal/ac'],
  },
  fertilizing: {
    metric: ['kg/ha', 'l/ha'],
    imperial: ['lb/ac', 'gal/ac'],
    hybrid: ['lb/ac', 'gal/ac'],
  },
};

export const getRatesUnitByMaterial = (
  type: VRAMapInputType,
  unitSystem: UnitSystem,
  material?: MaterialClassification,
) => {
  const newUnit = RateUnitsByType[type][unitSystem].find(
    (unit) => UnitsToMaterialClassification[unit] === material,
  );
  return newUnit || RateUnitsByType[type][unitSystem][0]!;
};
