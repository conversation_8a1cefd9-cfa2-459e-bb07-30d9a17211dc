import { Meta } from '@storybook/react';
import { Source, Layer } from 'react-map-gl';
import { useEffect } from 'react';
import { useStore } from 'effector-react';

import { MapView } from 'components/MapView';
import Button from 'components/Button/Button';
import {
  zones$,
  fieldBorder$,
  cursor$,
  mode$,
  brushColor$,
  brushSize$,
  modeChanged,
  brushColorChanged,
  brushSizeChanged,
  editorLoaded,
} from './models/editor';
import { ZoneName } from 'models/vraMaps';
import { ZoneIndexesByName } from 'constants/zones';
import { getVRAZoneColor } from 'utils/zones';

import DemoZones from 'assets/map/zones_demo_2.json';
import DemoFieldBorder from 'assets/map/zones_demo_2_field.json';

export default {
  title: 'Experiments/Zone Editor',
} as Meta;

export const Vector = () => {
  const zones = useStore(zones$);
  const fieldBorder = useStore(fieldBorder$);
  const cursor = useStore(cursor$);

  const brushColor = useStore(brushColor$);
  const brushSize = useStore(brushSize$);
  const mode = useStore(mode$);

  useEffect(() => {
    // Ugly: map cannot zoom to bbox until it's sise is initialized, which is also
    // happening in MapView useEffect, so we need something that triggers later
    // than our useEffect
    setTimeout(() => {
      editorLoaded({
        zones: DemoZones as any,
        fieldBorder: DemoFieldBorder,
      });
    }, 10);
  }, []);

  return (
    <MapView id="story">
      <div>
        {(['fill', 'draw'] as const).map((m) => (
          <Button
            key={m}
            fitContent
            theme={m === mode ? 'dark' : 'default'}
            size="small"
            onClick={() => {
              modeChanged(m);
            }}
          >
            {m}
          </Button>
        ))}

        {mode === 'draw' && (
          <span>
            {' '}
            <Button
              size="small"
              theme="default"
              fitContent
              onClick={() => {
                brushSizeChanged(brushSize - 10);
              }}
            >
              -
            </Button>
            <span style={{ backgroundColor: 'white' }}>{brushSize}m</span>
            <Button
              size="small"
              theme="default"
              fitContent
              onClick={() => {
                brushSizeChanged(brushSize + 10);
              }}
            >
              +
            </Button>
          </span>
        )}

        {mode !== null && (
          <span>
            {' '}
            {([null, 'm1', 'p0', 'p1'] as const).map((zoneName) => (
              <Button
                key={zoneName || 'null'}
                size="small"
                fitContent
                theme={brushColor === zoneName ? 'dark' : 'default'}
                onClick={() => {
                  brushColorChanged(zoneName);
                }}
              >
                {zoneName || 'erase'}
              </Button>
            ))}
          </span>
        )}
      </div>

      {Object.entries(zones).map(([zoneName, geojson]) => (
        <Source
          key={zoneName}
          id={`zone-${zoneName}`}
          type="geojson"
          data={geojson}
        >
          <Layer
            id={`zone-${zoneName}`}
            type="fill"
            paint={{
              'fill-color': getVRAZoneColor(
                3,
                ZoneIndexesByName[zoneName as ZoneName],
              ),
            }}
          />
          <Layer
            id={`zone-${zoneName}-border`}
            type="line"
            paint={{ 'line-width': 1 }}
          />
        </Source>
      ))}

      {mode === 'draw' && (
        <Source id="cursor" type="geojson" data={cursor}>
          <Layer id="cursor" type="line" paint={{ 'line-width': 2 }} />
        </Source>
      )}

      {fieldBorder && (
        <Source
          id="field"
          type="geojson"
          data={{
            type: 'FeatureCollection',
            features: [
              { type: 'Feature', properties: {}, geometry: fieldBorder as any },
            ],
          }}
        >
          <Layer id="field" type="line" paint={{ 'line-width': 2 }} />
        </Source>
      )}
    </MapView>
  );
};
