import {
  StoreValue,
  combine,
  createEffect,
  createStore,
  createEvent,
  sample,
} from 'effector';
import {
  FeatureCollection,
  Feature,
  Geometry,
  MultiPolygon,
  Polygon,
  featureCollection,
} from '@turf/helpers';
import circle from '@turf/circle';
import bbox from '@turf/bbox';
import bboxPolygon from '@turf/bbox-polygon';
import booleanPointInPolygon from '@turf/boolean-point-in-polygon';
import difference from '@turf/difference';
import intersect from '@turf/intersect';
import union from '@turf/union';

import { ZoneName, Zones } from 'models/vraMaps';
import {
  allowDragging$,
  hoveredPosition$,
  mouseMoved,
  mousePressed,
  mouseReleased,
  zoomedToBbox,
} from 'models/map';

export const editorLoaded =
  createEvent<{ zones: Zones; fieldBorder: Geometry }>();
export const modeChanged = createEvent<'draw' | 'fill' | 'erase'>();
export const brushColorChanged = createEvent<ZoneName | null>();
export const brushSizeChanged = createEvent<number>();

export const mode$ = createStore<null | 'draw' | 'fill' | 'erase'>(null);
export const brushColor$ = createStore<ZoneName | null>(null);
export const brushSize$ = createStore(10);
export const mousePressed$ = createStore(false);
export const fieldBorder$ = createStore<Geometry | null>(null);

export const zones$ = createStore<{
  [zoneName in ZoneName]?: FeatureCollection<MultiPolygon | Polygon>;
}>({});

export const cursor$ = combine(
  hoveredPosition$,
  brushSize$,
  (hoveredPosition, brushSize) => {
    const feature = circle(
      [hoveredPosition.longitude, hoveredPosition.latitude],
      brushSize / 1000 / 2, // this is radius, so divide by 2
      { steps: 4 },
    );
    const box = bbox(feature);
    const polygon = bboxPolygon(box);
    return featureCollection([polygon]);
  },
);

const drawBrushFx = createEffect(
  ({
    zones,
    brush,
    brushColor,
  }: {
    zones: StoreValue<typeof zones$>;
    brush: FeatureCollection<Polygon>;
    brushColor: ZoneName | null;
  }) => {
    const brushFeature = brush.features[0];
    const nextZones: typeof zones = { ...zones };

    if (!brushFeature) {
      // Nothing to draw
      return zones;
    }

    // Cut brush from all zones but target one
    for (const [zoneName, geojson] of Object.entries(zones)) {
      if (zoneName === brushColor) {
        continue;
      }
      // Substract brush from everything except target zone
      nextZones[zoneName as ZoneName] = featureCollection(
        geojson.features
          .map((f) => difference(f, brushFeature))
          .filter((f): f is Feature<MultiPolygon | Polygon> => !!f),
      );
    }

    if (brushColor) {
      // Add brush to target zone
      if (nextZones[brushColor]) {
        const features = [
          ...nextZones[brushColor]!.features,
          brush.features[0]!,
        ];
        const combined = features.reduce(
          (result, feature) => union(result, feature)!,
        );
        nextZones[brushColor] = featureCollection([combined]);
      } else {
        nextZones[brushColor] = featureCollection([brush.features[0]!]);
      }
    }

    return nextZones;
  },
);

const fillFx = createEffect(
  ({
    zones,
    brushColor,
    hoveredPosition,
  }: {
    zones: StoreValue<typeof zones$>;
    brushColor: ZoneName | null;
    hoveredPosition: { longitude: number; latitude: number };
  }) => {
    let hoveredFeature: Feature<Polygon> | null = null;
    let hoveredZoneName: ZoneName | null = null;

    for (const [zoneName, geojson] of Object.entries(zones)) {
      for (const feature of geojson.features) {
        if (feature.geometry.type === 'MultiPolygon') {
          for (const polygonCoordinates of feature.geometry.coordinates) {
            const polygon = {
              type: 'Polygon',
              coordinates: polygonCoordinates,
            } as const;
            const hovered = booleanPointInPolygon(
              [hoveredPosition.longitude, hoveredPosition.latitude],
              polygon,
            );
            if (hovered) {
              hoveredZoneName = zoneName as ZoneName;
              hoveredFeature = {
                type: 'Feature',
                properties: {},
                geometry: polygon,
              };
            }
          }
        }
        if (feature.geometry.type === 'Polygon') {
          const hovered = booleanPointInPolygon(
            [hoveredPosition.longitude, hoveredPosition.latitude],
            feature,
          );
          if (hovered) {
            hoveredZoneName = zoneName as ZoneName;
            hoveredFeature = feature as Feature<Polygon>;
            break;
          }
        }
      }
    }

    if (!hoveredFeature || !hoveredZoneName) {
      return;
    }

    drawBrushFx({
      zones,
      brush: featureCollection([hoveredFeature]),
      brushColor,
    });
  },
);

const clipByFieldFx = createEffect(
  ({
    zones,
    fieldBorder,
  }: {
    zones: StoreValue<typeof zones$>;
    fieldBorder: Geometry | null;
  }) => {
    if (!fieldBorder) {
      return zones;
    }

    const nextZones: StoreValue<typeof zones$> = {};

    const fieldBorderFeature = {
      type: 'Feature',
      geometry: fieldBorder,
      // This should generally be true. Should we update fieldSeason geom type?
    } as Feature<MultiPolygon | Polygon>;

    for (const [zoneName, geojson] of Object.entries(zones)) {
      nextZones[zoneName as ZoneName] = featureCollection(
        geojson.features
          .map((f) => intersect(f, fieldBorderFeature))
          .filter((f): f is Feature<MultiPolygon | Polygon> => !!f),
      );
    }
    return nextZones;
  },
);

mode$.on(modeChanged, (currentMode, mode) =>
  mode === currentMode ? null : mode,
);

brushColor$.on(brushColorChanged, (_, color) => color);

brushSize$.on(brushSizeChanged, (_, size) => size);

mousePressed$.on(mousePressed, () => true).on(mouseReleased, () => false);

zones$
  .on(editorLoaded, (_, { zones }) => {
    const result: StoreValue<typeof zones$> = {};
    for (const feature of zones.features) {
      result[feature.properties.zone_name] = featureCollection([feature]);
    }
    return result;
  })
  .on(drawBrushFx.doneData, (_, zones) => zones)
  .on(clipByFieldFx.doneData, (_, zones) => zones);

fieldBorder$.on(editorLoaded, (_, { fieldBorder }) => fieldBorder);

// Zoom to field on load
sample({
  clock: editorLoaded,
  fn: ({ zones }) => ({
    bbox: bbox(zones),
    padding: 64,
  }),
  target: zoomedToBbox,
});

// Block map dragging while we are in draw mode
sample({
  source: mode$,
  fn: (mode) => mode == null,
  target: allowDragging$,
});

// Handle drawing
sample({
  clock: mouseMoved,
  source: {
    mode: mode$,
    zones: zones$,
    brush: cursor$,
    brushColor: brushColor$,
    mousePressed: mousePressed$,
  },
  filter: ({ mousePressed, mode }) => mode === 'draw' && mousePressed,
  target: drawBrushFx,
});

// Handle filling
sample({
  clock: mousePressed,
  source: {
    mode: mode$,
    zones: zones$,
    hoveredPosition: hoveredPosition$,
    brushColor: brushColor$,
  },
  filter: ({ mode }) => mode === 'fill',
  target: fillFx,
});

// Clip by field on finishing
sample({
  clock: mouseReleased,
  source: {
    mode: mode$,
    zones: zones$,
    fieldBorder: fieldBorder$,
  },
  filter: ({ mode }) => mode === 'draw',
  target: clipByFieldFx,
});
