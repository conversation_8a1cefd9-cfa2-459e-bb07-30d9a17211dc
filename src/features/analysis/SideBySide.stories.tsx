import { css } from '@linaria/core';
import { styled } from '@linaria/react';
import { Meta, Story } from '@storybook/react';

import SideBySide, { Props } from './SideBySide';

export default {
  title: 'Features/analysis/SideBySide',
  component: SideBySide,
} as Meta;

const Wrapper = styled.div`
  width: 400px;
  margin-left: auto;
  margin-right: auto;
  margin-top: 20px;
`;

const container = css`
  width: 100%;
  min-height: 200px;
`;

const image = css`
  width: 100%;
  height: 100%;
  object-fit: cover;
`;

const Image1 =
  'https://generative-placeholders.glitch.me/image?width=600&height=300&style=123';
const Image2 =
  'https://generative-placeholders.glitch.me/image?width=600&height=300&style=circles';

export const Default: Story<Props> = () => (
  <Wrapper>
    <SideBySide
      className={container}
      left={<img className={image} src={Image1} alt="" />}
      right={<img className={image} src={Image2} alt="" />}
    />
  </Wrapper>
);

export const WithAutoCenter: Story<Props> = () => (
  <Wrapper>
    <SideBySide
      className={container}
      left={<img className={image} src={Image1} alt="" />}
      right={<img className={image} src={Image2} alt="" />}
      autoCenter
    />
  </Wrapper>
);
