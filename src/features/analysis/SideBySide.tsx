import React, { ReactNode, useEffect, useRef, useState } from 'react';
import { animated, useSpring } from 'react-spring';
import cx from 'classnames';

import SvgIcon from 'components/SvgIcon';
import { isSafari } from 'utils/devices';

import classNames from './SideBySide.module.css';

export type Props = {
  className?: string;
  left: ReactNode;
  right: ReactNode;
  autoCenter?: boolean;
  children?: ReactNode;
};

function SideBySide({ className, autoCenter, children, left, right }: Props) {
  const [sliderStyle, animation] = useSpring(() => ({ left: '50%' }));
  const [mapPercent, setMapPercent] = useState(50);

  const containerRef = useRef<HTMLDivElement>(null);
  const sliderRef = useRef<HTMLDivElement>(null);
  const leftMapRef = useRef<HTMLDivElement>(null);
  const rightMapRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const containerElement = containerRef.current!;
    const sliderElement = sliderRef.current!;

    let currentContainerWidth = containerElement.getBoundingClientRect().width;

    const startSliding = (e: TouchEvent | MouseEvent) => {
      // Prevent default behavior other than mobile scrolling
      if (!('touches' in e)) {
        e.preventDefault();
      }

      currentContainerWidth = containerElement.getBoundingClientRect().width;

      // Slide the image even if you just click or tap (not drag)
      handleSliding(e);

      window.addEventListener('mousemove', handleSliding);
      window.addEventListener('touchmove', handleSliding);
    };

    const handleSliding = (event: TouchEvent | MouseEvent) => {
      const e = event || window.event;

      const cursorXfromViewport =
        'touches' in e ? e.touches[0]!.pageX : e.pageX;
      const cursorXfromWindow = cursorXfromViewport - window.pageXOffset;

      const mapPosition = rightMapRef.current!.getBoundingClientRect();

      let pos = cursorXfromWindow - mapPosition.left;

      if (pos < 1) pos = 1;
      if (pos > currentContainerWidth - 1) pos = currentContainerWidth - 1;

      const mapPercent = (pos * 100) / currentContainerWidth;
      animation.set({ left: `${mapPercent}%` });
      setMapPercent(mapPercent);
    };

    const finishSliding = () => {
      window.removeEventListener('mousemove', handleSliding);
      window.removeEventListener('touchmove', handleSliding);
      if (autoCenter) {
        animation.start({ left: '50%' });
        setMapPercent(50);
      }
    };

    sliderElement.addEventListener('mousedown', startSliding);
    window.addEventListener('mouseup', finishSliding);

    // for mobile
    sliderElement.addEventListener('touchstart', startSliding);
    window.addEventListener('touchend', finishSliding);

    return () => {
      finishSliding();
      sliderElement.removeEventListener('mousedown', startSliding);
      sliderElement.removeEventListener('touchstart', startSliding);
      window.removeEventListener('mouseup', finishSliding);
      window.removeEventListener('touchend', finishSliding);
    };
  }, [animation, autoCenter]);

  // Due to safari bug https://bugs.webkit.org/show_bug.cgi?id=152548,
  // clip-path works visually, but clicks go to wrong side of the map.
  // We hotfix it here by manipulating z-index of halves as mouse moves over
  const applySafariHack = (event: React.MouseEvent) => {
    let containerBox = containerRef.current!.getBoundingClientRect();

    const cursorXfromViewport = event.pageX;
    const cursorXfromWindow = cursorXfromViewport - window.pageXOffset;
    const cursorXfromContainer = cursorXfromWindow - containerBox.left;

    const hoveredLeftMap =
      cursorXfromContainer < (containerBox.width * mapPercent) / 100;

    (hoveredLeftMap ? leftMapRef : rightMapRef).current!.style.zIndex = '2';
    (hoveredLeftMap ? rightMapRef : leftMapRef).current!.style.zIndex = '1';
  };

  const leftClipPath = `polygon(0% 0%, ${mapPercent}% 0%, ${mapPercent}% 100%, 0% 100%)`;
  const rightClipPath = `polygon(${mapPercent}% 0%, 100% 0%, 100% 100%, ${mapPercent}% 100%)`;

  return (
    <>
      <div
        ref={containerRef}
        className={cx(classNames.container, className)}
        onMouseMove={isSafari() ? applySafariHack : undefined}
      >
        <div
          ref={leftMapRef}
          className={classNames.left}
          style={{
            clipPath: leftClipPath,
            WebkitClipPath: leftClipPath,
          }}
        >
          {left}
        </div>
        <div
          ref={rightMapRef}
          className={classNames.right}
          style={{
            clipPath: rightClipPath,
            WebkitClipPath: rightClipPath,
          }}
        >
          {right}
        </div>

        <animated.div
          ref={sliderRef}
          className={classNames.slider}
          style={sliderStyle}
        >
          <SvgIcon name="CompareSlider" width={40} height={40} />
        </animated.div>

        {children}
      </div>
    </>
  );
}

export default SideBySide;
