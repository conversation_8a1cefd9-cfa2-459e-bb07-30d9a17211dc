.container {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.label {
  font-weight: 500;
  margin-bottom: 12px;
}

.vraInputs {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
  gap: 16px;
}

.zoneContainer {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.zoneTitle {
  font-weight: 500;
  font-size: 12px;
}

.zoneBody {
  flex: 1;
  display: flex;
  gap: 8px;
}

.zoneIndicator {
  width: 4px;
  border-radius: 2px;
}

.zoneValueList {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
}

.rateValue {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
