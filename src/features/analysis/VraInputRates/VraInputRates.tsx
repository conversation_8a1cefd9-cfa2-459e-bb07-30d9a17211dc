import React, { FC } from 'react';
import { useTranslation } from 'react-i18next';
import { useStore } from 'effector-react';

import { VRAMap } from 'models/vraMaps';
import {
  calculateZoneIndexes,
  getVRAZoneColor,
  getZoneCategory,
  getZoneTitle,
} from 'utils/zones';
import { ZoneNamesByIndex } from 'constants/zones';
import { getColorPalleteBySource, getInputsArray } from 'utils/vra';
import { unitSystem$ } from 'models/settings';
import { findMatchingUnitInAnotherSystem } from '../utils/rate-units';

import classNames from './VraInputRates.module.css';

type Props = {
  vraMap: VRAMap;
};

export const VraInputRates: FC<Props> = ({ vraMap }) => {
  const { i18n, t } = useTranslation();
  const unitSystem = useStore(unitSystem$);
  const inputList = getInputsArray(vraMap);
  const zoneCount = vraMap.productivity_zones.count;
  const source = vraMap.productivity_zones.source;
  const zones = vraMap.productivity_zones.zones;
  const palette = getColorPalleteBySource(source);

  return (
    <div className={classNames.container}>
      <div className={classNames.label}>{t('vra-input-rates.label')}</div>
      <div className={classNames.vraInputs}>
        {calculateZoneIndexes(zones).map((zoneIndex) => {
          const zoneCategory = getZoneCategory(zoneCount, zoneIndex);
          const zoneTitle = getZoneTitle(i18n, zoneCount, zoneIndex);
          const sourceKey = 'soil-sampling.soil-sampling-form.wrapping-type';
          const mainZoneTitle = zoneCategory
            ? ` (${t(`${sourceKey}.${zoneCategory}`)})`
            : '';
          const backgroundColor = getVRAZoneColor(
            zoneCount,
            zoneIndex,
            palette,
          );

          return (
            <div key={zoneIndex} className={classNames.zoneContainer}>
              <div className={classNames.zoneTitle}>
                {`${zoneTitle}${mainZoneTitle}`}
              </div>
              <div className={classNames.zoneBody}>
                <div
                  className={classNames.zoneIndicator}
                  style={{ backgroundColor }}
                />
                <div className={classNames.zoneValueList}>
                  {inputList.map((input, idx) => {
                    const inputRate = input.rates[ZoneNamesByIndex[zoneIndex]!];
                    const targetRateUnit = findMatchingUnitInAnotherSystem(
                      vraMap.unit_system || unitSystem,
                      unitSystem,
                      input.type,
                      input.rates_unit,
                    );
                    const columnName = input.column_name
                      ? `${input.column_name} `
                      : '';
                    let rate = t('vra.card.rates.no_data');
                    let rateUnit = '';
                    if (inputRate && inputRate > 0) {
                      rate = inputRate.toString();
                      rateUnit = t(`units.${targetRateUnit}`);
                    }
                    return (
                      <div key={idx} className={classNames.rateValue}>
                        {columnName}
                        {rate} {rateUnit}
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};
