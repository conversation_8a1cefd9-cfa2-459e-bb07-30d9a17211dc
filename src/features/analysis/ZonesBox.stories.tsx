import { styled } from '@linaria/react';
import { Meta, Story } from '@storybook/react';

import ZonesBox, { Props } from './ZonesBox';

import DemoZones from 'assets/map/zones_demo_2.json';
import DemoControlLines from 'assets/map/zones_demo_2_stripe.json';
import { makeProductivityZone, makeSowingVraMap } from '../../utils/mocks';

export default {
  title: 'Features/analysis/ZonesBox',
  component: ZonesBox,
} as Meta;

const Wrapper = styled.div`
  width: 400px;
  height: 400px;
  margin-left: auto;
  margin-right: auto;
  margin-top: 20px;
`;

export const Default: Story<Props> = () => (
  <Wrapper>
    <ZonesBox
      vraMap={makeSowingVraMap({
        productivity_zones: makeProductivityZone({ zones: DemoZones as any }),
      })}
    />
  </Wrapper>
);

export const WithControlLines: Story<Props> = () => (
  <Wrapper>
    <ZonesBox
      vraMap={makeSowingVraMap({
        overlaid_control_lines: DemoControlLines as any,
        productivity_zones: makeProductivityZone({ zones: DemoZones as any }),
      })}
    />
  </Wrapper>
);
