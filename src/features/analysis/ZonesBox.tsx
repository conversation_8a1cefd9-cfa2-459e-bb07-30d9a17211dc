import { Feature, feature, featureCollection } from '@turf/helpers';
import { useCallback, useMemo, useState } from 'react';
import { useStore } from 'effector-react';

import { VRAMap, ZoneName } from 'models/vraMaps';
import { unitSystem$ } from 'models/settings';
import { ZoneIndexesByName } from 'constants/zones';
import { getVRAZoneColor } from 'utils/zones';
import { getColorPalleteBySource, getRateTooltipValues } from 'utils/vra';

import RateTooltip from './RateTooltip';
import GeometryRenderer from './GeometryRenderer';

import classNames from './ZonesBox.module.css';

const getFeatureStyle = (
  feature: Feature,
  palette: { [count: number]: string[] },
) => {
  const { properties } = feature;

  // Control line
  if (properties?.zone_name_line) {
    return {
      stroke: 'black',
      weight: 1,
    };
  }

  // Zone
  if (properties?.zone_name) {
    const zoneName = properties.zone_name as ZoneName;
    const zoneIndex = ZoneIndexesByName[zoneName];
    return {
      stroke: 'black',
      weight: 1,
      fill: getVRAZoneColor(properties.zonesCount, zoneIndex, palette),
    };
  }

  // This should render nothing
  return {};
};

export type Props = {
  vraMap: VRAMap;
};

function ZonesBox({ vraMap }: Props) {
  const zones = vraMap.productivity_zones.zones;
  const lines = vraMap.overlaid_control_lines || undefined;
  const zonesCount = vraMap.productivity_zones.count;
  const palette = getColorPalleteBySource(vraMap.productivity_zones.source);

  const [hoverInfo, setHoverInfo] = useState<{
    values: string[];
    x: number;
    y: number;
  } | null>(null);

  const unitSystem = useStore(unitSystem$);

  const handleHover = useCallback(
    (event, feature) => {
      if (!feature) {
        setHoverInfo(null);
        return;
      }
      const tooltipZoneName = (feature.properties?.zone_name_line ||
        feature.properties?.zone_name) as ZoneName;
      const rateTooltipValues = getRateTooltipValues(
        vraMap,
        unitSystem,
        tooltipZoneName,
      );
      setHoverInfo({
        values: rateTooltipValues,
        x: event.pageX,
        y: event.pageY,
      });
    },
    [vraMap, unitSystem],
  );

  const geoJSON = useMemo(
    () =>
      featureCollection([
        ...zones.features.map((f) =>
          feature(f.geometry, {
            ...f.properties,
            zonesCount,
          }),
        ),
        ...(lines ? lines.features : []).map((f) =>
          feature(f.geometry, {
            ...f.properties,
            zonesCount,
            type: 'stripes',
            stripesFill: getVRAZoneColor(
              zonesCount,
              ZoneIndexesByName[f.properties.zone_name_line],
              palette,
            ),
          }),
        ),
      ]),
    [lines, zones, zonesCount, palette],
  );

  return (
    <div className={classNames.container}>
      <GeometryRenderer
        className={classNames.geometry}
        styles={(feature) => getFeatureStyle(feature, palette)}
        geojson={geoJSON}
        onHoverFeature={handleHover}
      />
      {hoverInfo && (
        <RateTooltip
          x={hoverInfo.x}
          y={hoverInfo.y}
          className={classNames.rateTooltip}
        >
          {hoverInfo.values.map((value, index) => (
            <div key={index} className={classNames.rowRate}>
              {value}
            </div>
          ))}
        </RateTooltip>
      )}
    </div>
  );
}

export default ZonesBox;
