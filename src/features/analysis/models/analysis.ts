import { combine, createEvent, createStore, sample } from 'effector';
import { createGate } from 'effector-react';

import {
  fetchMachineDataFx,
  MachineData,
} from 'features/field-page/models/machine-data';
import { fetchFieldSeasonReportFx } from 'features/field-page/models/report';
import {
  fetchFieldSeasonFx,
  FieldSeason,
  fieldSeasonsByUuid$,
} from 'models/fieldSeasons';
import { VRAMap, vraMapsByUuid$ } from 'models/vraMaps';
import { fetchAnalysisFx } from './jobs';
import { Analysis } from './types';

type AnalysisPageGateParams = {
  machineDataUuid: MachineData['uuid'];
  fieldSeasonUuid: FieldSeason['uuid'];
  paInfoUuid: string | null;
};

export const AnalysisPageGate = createGate<AnalysisPageGateParams>();

const selectedMachineDataUuid = AnalysisPageGate.state.map(
  (gate) => gate.machineDataUuid || null,
);

export const analysis$ = createStore<Analysis | null>(null);

export const selectedCompareTarget$ = createStore<
  | { type: 'default' }
  | { type: 'vra-map'; vraMapUuid: string }
  | { type: 'productivity-map' }
>({ type: 'default' });

const fetchesPending$ = combine(
  fetchFieldSeasonFx.pending,
  fetchMachineDataFx.pending,
  fetchFieldSeasonReportFx.pending,
  (fsPending, mdPending, fsrPending) => fsPending || mdPending || fsrPending,
);

export const compareTarget$ = combine(
  AnalysisPageGate.state,
  vraMapsByUuid$,
  fetchesPending$,
  selectedCompareTarget$,
  (
    gate,
    vraMapsByUuid,
    fetchesPending,
    selectedCompareTarget,
  ):
    | { type: 'vra-map'; vraMap: VRAMap | null }
    | { type: 'productivity-map' }
    | { type: 'loading' } => {
    if (selectedCompareTarget.type === 'productivity-map') {
      return { type: 'productivity-map' };
    }

    if (selectedCompareTarget.type === 'vra-map') {
      return {
        type: 'vra-map',
        vraMap: vraMapsByUuid[selectedCompareTarget.vraMapUuid] || null,
      };
    }

    if (fetchesPending) {
      return { type: 'loading' };
    }

    const vraMaps = Object.values(vraMapsByUuid).filter(
      (item) => item.field_user_season_uuid === gate.fieldSeasonUuid,
    );

    if (vraMaps[0]) {
      return { type: 'vra-map', vraMap: vraMaps[0] };
    }

    return { type: 'productivity-map' };
  },
);

export const compareTargetSelected = createEvent<
  { type: 'productivity-map' } | { type: 'vra-map'; vraMapUuid: string }
>();

export const resetAnalysis = createEvent();

analysis$
  .on(resetAnalysis, () => null)
  .on(fetchAnalysisFx, () => null)
  .on(fetchAnalysisFx.doneData, (_, analysis) => analysis)
  .reset([AnalysisPageGate.open]);

selectedCompareTarget$
  .on(compareTargetSelected, (_, payload) => payload)
  .reset(AnalysisPageGate.open);

// Fetch machine data on open page
sample({
  source: selectedMachineDataUuid,
  filter: Boolean,
  target: fetchMachineDataFx,
});

// Fetch all vra maps for this field (to show in dropdown)
sample({
  source: AnalysisPageGate.open,
  fn: ({ fieldSeasonUuid }) => fieldSeasonUuid,
  target: fetchFieldSeasonFx,
});

// Fetch productivity report for this field (to show dates in dropdown)
sample({
  clock: AnalysisPageGate.open,
  filter: ({ paInfoUuid }) => !!paInfoUuid,
  fn: ({ paInfoUuid }) => paInfoUuid!,
  target: fetchFieldSeasonReportFx,
});

// Fetch analysis
sample({
  source: {
    gate: AnalysisPageGate.state,
    isOpen: AnalysisPageGate.status,
    compareTarget: compareTarget$,
  },
  filter: ({ isOpen, compareTarget }) =>
    compareTarget.type === 'vra-map' && !!compareTarget.vraMap && isOpen,
  fn: ({ gate, compareTarget }) => ({
    fieldSeasonUuid: gate.fieldSeasonUuid,
    machineDataUuid: gate.machineDataUuid,
    vraMapUuid: (compareTarget as { type: 'vra-map'; vraMap: VRAMap }).vraMap
      .uuid,
  }),
  target: fetchAnalysisFx,
});

// Reset analysis if no control lines
sample({
  source: {
    isOpen: AnalysisPageGate.status,
    compareTarget: compareTarget$,
  },
  filter: ({ isOpen, compareTarget }) => isOpen,
  target: resetAnalysis,
});

// Fetch fieldSeason if no has_analyses
sample({
  clock: fetchAnalysisFx.done,
  source: fieldSeasonsByUuid$,
  filter: (fieldSeasons, { params }) => {
    return !Object.keys(fieldSeasons).some(
      (key) => !!fieldSeasons[key] && !!fieldSeasons[key]!.has_analyses,
    );
  },
  fn: (_, { params }) => params.fieldSeasonUuid,
  target: fetchFieldSeasonFx,
});
