import { createEffect } from 'effector';

import { request } from 'utils/api';
import { Analysis } from './types';

const PollInterval = 1000;

const CommonQuery = {
  'include[]': [
    'field_user_season.uuid',
    'machine_data.uuid',
    'vra_map.uuid',
    'analyze_data_zone_infos',
    'analyze_data_control_line_results',
  ],
  'exclude[]': ['field_user_season.*', 'machine_data.*', 'vra_map.*'],
};

const delay = (timeout: number) =>
  new Promise((resolve) => setTimeout(resolve, timeout));

const intervalSetter = () => {
  let interval = PollInterval;
  return () => {
    interval += 2000; // increase on 2s each time
    return interval;
  };
};

export const waitForAnalysisCompletionFx = createEffect(
  async (uuid: string) => {
    const getInterval = intervalSetter();
    while (true) {
      await delay(getInterval());

      // TODO: Ignore network errors (at least some finite number)?
      const job = await request<Analysis>(
        `fields-users-seasons-analyze-data/${uuid}`,
        'GET',
        {
          query: { ...CommonQuery },
        },
      );

      if (job.status === 'finished') {
        return job;
      }

      if (job.status === 'failed') {
        throw new Error('Analysis job failed');
      }
    }
  },
);

export const fetchAnalysisFx = createEffect(
  async ({
    fieldSeasonUuid,
    machineDataUuid,
    vraMapUuid,
  }: {
    fieldSeasonUuid: string;
    machineDataUuid: string;
    vraMapUuid: string;
  }) => {
    const filters = {
      'filter{field_user_season.uuid}': fieldSeasonUuid,
      'filter{machine_data.uuid}': machineDataUuid,
      'filter{vra_map.uuid}': vraMapUuid,
    };

    const [existingJob] = await request<Analysis[]>(
      'fields-users-seasons-analyze-data',
      'GET',
      {
        query: {
          ...CommonQuery,
          ...filters,
        },
      },
    );

    if (existingJob?.status === 'failed') {
      throw new Error('Analysis job failed');
    }

    if (existingJob?.status === 'finished') {
      return existingJob;
    }

    if (existingJob) {
      return await waitForAnalysisCompletionFx(existingJob.uuid);
    }

    let newJob = await request<Analysis>(
      'fields-users-seasons-analyze-data',
      'POST',
      {
        query: { ...CommonQuery },
        body: {
          field_user_season_uuid: fieldSeasonUuid,
          machine_data_uuid: machineDataUuid,
          vra_map_uuid: vraMapUuid,
        },
      },
    );

    return await waitForAnalysisCompletionFx(newJob.uuid);
  },
);
