import { ZoneName } from 'models/vraMaps';

export type AnalysisZoneInfo = {
  id: number;
  zone_name: ZoneName;
  mean_data_value: number;
};

export type ControlLineResult = {
  id: number;
  zone_name: ZoneName;
  control_line_name: ZoneName;
  delta: number | null;
  confidence: number;
};

export type Analysis = {
  uuid: string;
  field_user_season_uuid: string;
  machine_data_uuid: string;
  sowing_vra_map_uuid: string;
  created_at: string;
  updated_at: string;
  status: 'created' | 'processing' | 'finished' | 'failed';
  analyze_data_zone_infos?: AnalysisZoneInfo[];
  analyze_data_control_line_results?: ControlLineResult[];
  total_mean_data_value: number | null;
  predicted_average_data_without_vra: number | null;
  average_seeding_rate_with_vra: number | null;
  average_seeding_rate_without_vra: number | null;
};
