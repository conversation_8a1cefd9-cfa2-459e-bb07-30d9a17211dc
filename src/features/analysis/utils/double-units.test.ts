import { expect, describe, it } from 'vitest';

import { convertDoubleUnit } from './double-units';

describe('convertDoubleUnit', () => {
  it('converts to same unit without loss', () => {
    expect(convertDoubleUnit('kg/ha', 'kg/ha', 42)).toBe(42);
  });

  it('converts to another up unit', () => {
    expect(convertDoubleUnit('kg/ha', 'g/ha', 42)).toBe(42000);
  });

  it('converts to another down unit', () => {
    expect(convertDoubleUnit('kg/ha', 'kg/ac', 42)).toBe(17);
  });

  it('converts to another unit', () => {
    expect(convertDoubleUnit('kg/ha', 'lb/ac', 42)).toBe(37.47);
  });

  it('does not care about seeds as up unit', () => {
    expect(convertDoubleUnit('seeds/ha', 'seeds/ac', 42)).toBe(17);
  });

  it('respects division by zero', () => {
    expect(convertDoubleUnit('seeds/ha', 'seeds/ac', 0)).toBe(0);
  });

  it('rounds with precision 2 if < 1000', () => {
    expect(convertDoubleUnit('kg/ha', 'kg/ha', 142.99)).toBe(142.99);
  });

  it('rounds down for precision 0 if >= 1000', () => {
    expect(convertDoubleUnit('kg/ha', 'kg/ha', 1420.45)).toBe(1420);
  });

  it('rounds up for precision 0 if >= 1000', () => {
    expect(convertDoubleUnit('kg/ha', 'kg/ha', 1420.99)).toBe(1421);
  });
});
