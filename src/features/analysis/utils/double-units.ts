import { DoubleUnit } from 'constants/units';
import { AllMeasuresUnits, convert } from 'utils/convert-units';
import { round } from 'utils/units';

const isUnitSupported = (unit: string) => {
  const supportedUnits = convert().possibilities();
  return supportedUnits.includes(unit as AllMeasuresUnits);
};

export const convertDoubleUnit = (
  sourceUnit: DoubleUnit,
  targetUnit: DoubleUnit,
  value: number,
) => {
  const [upSourceUnit, downSourceUnit] = sourceUnit.split('/') as [
    AllMeasuresUnits,
    AllMeasuresUnits,
  ];
  const [upTargetUnit, downTargetUnit] = targetUnit.split('/') as [
    AllMeasuresUnits,
    AllMeasuresUnits,
  ];

  const up = isUnitSupported(upSourceUnit)
    ? convert(1).from(upSourceUnit).to(upTargetUnit)
    : 0;
  const down = isUnitSupported(downSourceUnit)
    ? convert(1).from(downSourceUnit).to(downTargetUnit)
    : 0;

  if (down === 0) {
    return 0;
  }

  const converted = value * (up / down);
  const precision = converted >= 1000 || upSourceUnit === 'seeds' ? 0 : 2;
  const rounded = round(converted, precision);

  return rounded;
};
