import {
  MachineData,
  OperationType,
} from 'features/field-page/models/machine-data';

export enum MachineImageType {
  Image = 'image',
}

export const getColorMap = (operationType?: OperationType): string => {
  if (operationType === OperationType.Harvesting || !operationType) {
    return 'contrasted_rgb';
  }
  return operationType;
};

export const getMachineImageUrl = (
  machineData: MachineData,
  type: MachineImageType,
) => {
  const colorMap =
    machineData.operation_type === OperationType.Harvesting ||
    !machineData.operation_type
      ? 'contrasted_rgb'
      : machineData.operation_type;
  return `fields-users-seasons-machine-data/${machineData.uuid}/${type}/${colorMap}.png`;
};

export const isPercentOperationType = (machineData: MachineData | null) => {
  if (!machineData) {
    return false;
  }
  return (
    machineData?.operation_type === OperationType.Protein ||
    machineData?.operation_type === OperationType.Starch
  );
};
