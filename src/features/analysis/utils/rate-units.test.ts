import { describe, expect, it } from 'vitest';

import { RateUnitsByType } from 'constants/zones';
import { VRAMapInputType } from 'models/vraMaps';
import { findMatchingUnitInAnotherSystem } from './rate-units';

describe('findMatchingUnitInAnotherSystem', () => {
  it('works for kg/ha', () => {
    expect(
      findMatchingUnitInAnotherSystem(
        'metric',
        'imperial',
        VRAMapInputType.Sowing,
        'kg/ha',
      ),
    ).toBe('lb/ac');
  });

  it('can find a match for every possible input combination', () => {
    const vraMapTypes: VRAMapInputType[] = [
      VRAMapInputType.Sowing,
      VRAMapInputType.Spraying,
      VRAMapInputType.Fertilizing,
    ];
    const unitSystems = ['metric', 'imperial', 'hybrid'] as const;

    for (const vraMapType of vraMapTypes) {
      for (const sourceUnitSystem of unitSystems) {
        for (const targetUnitSystem of unitSystems) {
          const units = RateUnitsByType[vraMapType][sourceUnitSystem];
          for (const sourceUnit of units) {
            expect(
              findMatchingUnitInAnotherSystem(
                sourceUnitSystem,
                targetUnitSystem,
                vraMapType,
                sourceUnit,
              ),
            ).toBeTruthy();
          }
        }
      }
    }
  });
});
