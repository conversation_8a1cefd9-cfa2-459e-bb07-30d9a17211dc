import { RateUnitsByType } from 'constants/zones';
import { DoubleUnit, UnitSystem } from 'constants/units';

import { VRAMapInputType } from 'models/vraMaps';

export const findMatchingUnitInAnotherSystem = (
  sourceUnitSystem: UnitSystem,
  targetUnitSystem: UnitSystem,
  vraMapInputType: VRAMapInputType,
  unit: DoubleUnit,
) => {
  const index =
    RateUnitsByType[vraMapInputType][sourceUnitSystem].indexOf(unit);
  return RateUnitsByType[vraMapInputType][targetUnitSystem][index]!;
};
