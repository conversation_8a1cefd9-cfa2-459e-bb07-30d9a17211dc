import { expect, describe, it } from 'vitest';

import { makeCropDefinition, makeFile } from 'utils/mocks';

import {
  convertServerUnit,
  convertYieldUnit,
  getYieldTargetUnit,
  isYieldTargetUnitValid,
} from './yield-units';

describe('getYieldTargetUnit', () => {
  it('returns data_uom when nothing is selected', () => {
    const machineData = makeFile({ desired_uom: undefined, data_uom: 'kg' });
    expect(getYieldTargetUnit('metric', machineData, [])).toBe('kg');
    expect(getYieldTargetUnit('imperial', machineData, [])).toBe('kg');
  });

  it('returns stored unit if any', () => {
    const machineData = makeFile({ desired_uom: 'mg/m2' });
    expect(getYieldTargetUnit('metric', machineData, [])).toBe('mg/m2');
    expect(getYieldTargetUnit('imperial', machineData, [])).toBe('mg/m2');
  });

  it('keeps bushels when conversion rate is available', () => {
    const machineData = makeFile({ crop: 'alfalfa', desired_uom: 'bu/ac' });
    const crop = makeCropDefinition({ name: 'alfalfa' }, { kg_per_bu_rate: 2 });
    expect(getYieldTargetUnit('metric', machineData, [crop])).toBe('bu/ac');
  });

  it('discards bushels when conversion rate is not available', () => {
    const machineData = makeFile({ crop: 'alfalfa', desired_uom: 'bu/ac' });
    expect(getYieldTargetUnit('metric', machineData, [])).toBe('kg/ha');
  });
});

describe('convertServerUnit', () => {
  it('preserves units in client format', () => {
    expect(convertServerUnit('kg/ha')).toBe('kg/ha');
    expect(convertServerUnit('m2/ac')).toBe('m2/ac');
  });

  it('converts units without numbers', () => {
    expect(convertServerUnit('kg1ac-1')).toBe('kg/ac');
  });

  it('converts units with numbers', () => {
    expect(convertServerUnit('mg1[m2]-1')).toBe('mg/m2');
  });

  it('converts kg1[m2]-1 to kg/m2', () => {
    expect(convertServerUnit('kg1[m2]-1')).toBe('kg/m2');
  });
});

describe('isYieldTargetUnitValid', () => {
  it('returns true for standard metric units', () => {
    expect(isYieldTargetUnitValid('kg/ha')).toBe(true);
    expect(isYieldTargetUnitValid('q/ha')).toBe(true);
    expect(isYieldTargetUnitValid('mt/ha')).toBe(true);
  });

  it('returns true for kg/m2', () => {
    expect(isYieldTargetUnitValid('kg/m2')).toBe(true);
  });

  it('returns true for imperial units', () => {
    expect(isYieldTargetUnitValid('bu/ac')).toBe(true);
    expect(isYieldTargetUnitValid('lb/ac')).toBe(true);
  });

  it('returns false for unsupported units', () => {
    expect(isYieldTargetUnitValid('mg/m2')).toBe(false);
    expect(isYieldTargetUnitValid('foo/bar')).toBe(false);
  });
});

describe('convertYieldUnit', () => {
  it('works with default units of kg/ha', () => {
    const machineData = makeFile({
      desired_uom: undefined,
      data_uom: 'kg1ha-1',
    });

    expect(convertYieldUnit('metric', machineData, [], 42)).toBe(42);
  });

  it('converts to another units', () => {
    const machineData = makeFile({
      desired_uom: 'lb/ac',
      data_uom: 'kg1ha-1',
    });

    expect(convertYieldUnit('metric', machineData, [], 42)).toBe(37.47);
  });

  it('handles bushels', () => {
    const machineData = makeFile({
      crop: 'alfalfa',
      desired_uom: 'bu/ha',
      data_uom: 'kg/ha',
    });

    const crop = makeCropDefinition({ name: 'alfalfa' }, { kg_per_bu_rate: 2 });

    expect(convertYieldUnit('metric', machineData, [crop], 42)).toBe(21);
  });

  it('handles no bushels', () => {
    const machineData = makeFile({
      crop: 'alfalfa',
      desired_uom: 'bu/ac',
      data_uom: 'kg/ha',
    });

    expect(convertYieldUnit('imperial', machineData, [], 42)).toBe(37.47);
  });
});
