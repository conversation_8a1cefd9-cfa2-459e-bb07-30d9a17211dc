import { Crop } from 'features/vra/models/crops';
import { MachineData } from 'features/field-page/models/machine-data';

import { UnitSystem, DoubleUnit } from 'constants/units';
import { AllMeasuresUnits, convert } from 'utils/convert-units';
import { formatYieldValue } from 'utils/zones';
import { convertDoubleUnit } from './double-units';
import { convertBackendUnit, round } from 'utils/units';

type YieldUnits = 'bu' | AllMeasuresUnits;
type YieldDoubleUnit = `${YieldUnits}/${YieldUnits}`;

export const YieldSourceUnits: {
  category: 'metric' | 'imperial';
  units: YieldDoubleUnit[];
}[] = [
  {
    category: 'metric',
    units: ['mt/ha', 'q/ha', 'kg/ha'],
  },
  {
    category: 'imperial',
    units: ['bu/ac', 'lb/ac'],
  },
];

export const YieldTargetUnits: {
  [unitSystem in UnitSystem]: YieldDoubleUnit[];
} = {
  metric: ['kg/ha', 'q/ha', 'mt/ha', 'kg/m2'],
  imperial: ['bu/ac', 'lb/ac'],
  hybrid: ['bu/ac', 'lb/ac'],
};

export const isYieldTargetUnitValid = (unit: string) =>
  Object.values(YieldTargetUnits).some((units) =>
    units.includes(convertBackendUnit(unit) as YieldDoubleUnit),
  );

export const getYieldTargetUnit = (
  unitSystem: UnitSystem,
  machineData: MachineData,
  allCrops: Crop[],
) => {
  const unit = convertServerUnit(
    machineData.desired_uom || machineData.data_uom,
  );

  if (unit.includes('bu')) {
    const cropInfo = allCrops.find((crop) => crop.name === machineData.crop);
    const kgPerBu = cropInfo?.properties_map?.kg_per_bu_rate;
    if (!kgPerBu) {
      const firstNonBushelUnit = YieldTargetUnits[unitSystem].find(
        (unit) => !unit.includes('bu'),
      );
      return firstNonBushelUnit!;
    }
  }

  return unit as YieldDoubleUnit | string;
};

export const convertYieldUnit = (
  unitSystem: UnitSystem,
  machineData: MachineData,
  allCrops: Crop[],
  value: number,
) => {
  if (machineData.data_uom === 'prcnt') {
    return Number(value.toFixed(2));
  }
  const sourceUnit = convertServerUnit(machineData.data_uom);
  if (!isYieldTargetUnitValid(sourceUnit)) {
    return Number(value.toFixed(2));
  }
  const yieldTargetUnit = getYieldTargetUnit(unitSystem, machineData, allCrops);
  const targetUnit = convertServerUnit(yieldTargetUnit);

  // TODO: Can we ensure type safety here somehow?
  let [upSourceUnit, downSourceUnit] = sourceUnit.split('/') as [
    YieldUnits,
    YieldUnits,
  ];
  let [upTargetUnit, downTargetUnit] = targetUnit.split('/') as [
    YieldUnits,
    YieldUnits,
  ];

  // Conversion to bushels depends on a crop, so we pre-convert it here, before passing to convert-units
  if (upSourceUnit !== 'bu' && upTargetUnit === 'bu') {
    const cropInfo = allCrops.find((crop) => crop.name === machineData.crop);
    const kgPerBu = cropInfo?.properties_map?.kg_per_bu_rate;

    if (!kgPerBu) {
      // This should never happen, getYieldTargetUnit is expected to never allow bushels if we can't convert to them
      return -1;
    }

    const up = convert(1).from(upSourceUnit).to('kg') / kgPerBu;

    value *= up;
    upSourceUnit = 'pcs'; // just a dummy unit to disable up unit conversion (pcs are the same in every system)
    upTargetUnit = 'pcs';
  }

  if (upSourceUnit === 'bu') {
    if (upTargetUnit === 'bu') {
      const precision = value >= 1000 ? 0 : 2;
      const rounded = round(value, precision);
      return rounded;
    }
    const cropInfo = allCrops.find((crop) => crop.name === machineData.crop);
    const kgPerBu = cropInfo?.properties_map?.kg_per_bu_rate;

    if (!kgPerBu) {
      // This should never happen, getYieldTargetUnit is expected to never allow bushels if we can't convert to them
      return -1;
    }

    upSourceUnit = 'kg'; // just a dummy unit to disable up unit conversion (pcs are the same in every system)
    downSourceUnit = 'ha';
    value *= kgPerBu;
  }

  return convertDoubleUnit(
    `${upSourceUnit}/${downSourceUnit}` as DoubleUnit,
    `${upTargetUnit}/${downTargetUnit}` as DoubleUnit,
    value,
  );
};

export const convertServerUnit = (unit: string): YieldDoubleUnit | string => {
  // Crude check to see if unit is already in client-supported format
  if (unit.includes('/')) {
    return convertBackendUnit(unit) as YieldDoubleUnit;
  }

  const matches = unit.matchAll(
    /(?:\[(?<unescaped>.*)\])|(?<escaped>[A-Za-z]+)/g,
  );
  const parsed = [...matches].map(
    (match) => match?.groups?.unescaped || match?.groups?.escaped,
  );
  const [up, down] = parsed as [YieldUnits, YieldUnits];

  // If we don't have both parts, return the original unit
  if (!up || !down) {
    return unit;
  }

  return `${up}/${down}`;
};

export const yieldValueAccessor = (data: number, unit?: string) => {
  let precision = 0;
  if (!unit || !isYieldTargetUnitValid(unit)) {
    precision = 2;
  } else if (unit.includes('bu')) {
    precision = 0;
  } else {
    precision = 2;
  }
  const formatted = data.toFixed(precision);
  return formatYieldValue(parseFloat(formatted));
};
